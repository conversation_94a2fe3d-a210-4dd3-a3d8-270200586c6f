#!/usr/bin/env python3
"""
Test script to validate PO number extraction and fallback logic
"""

import sys
import os
sys.path.append('backend')

from backend.routers.webhook_sender import extract_document_number

def test_po_number_extraction():
    """Test various scenarios for PO number extraction"""
    
    print("=" * 60)
    print("TESTING PO NUMBER EXTRACTION AND FALLBACK LOGIC")
    print("=" * 60)
    
    # Test Case 1: Normal PO number in document_info
    print("\n1. Testing normal PO number in document_info:")
    test_data_1 = {
        "analysis": {
            "document_info": {
                "po_number": "PO123456"
            }
        }
    }
    result_1 = extract_document_number(test_data_1)
    print(f"Result: {result_1}")
    assert result_1["po_number"] == "PO123456", "Failed to extract normal PO number"
    print("✅ PASSED")
    
    # Test Case 2: No PO number, but has invoice number - should fallback
    print("\n2. Testing fallback to invoice number:")
    test_data_2 = {
        "analysis": {
            "document_info": {
                "invoice_number": "INV789012"
            }
        }
    }
    result_2 = extract_document_number(test_data_2)
    print(f"Result: {result_2}")
    assert result_2["po_number"] == "INV789012", "Failed to fallback to invoice number"
    assert result_2["invoice_number"] == "INV789012", "Failed to extract invoice number"
    print("✅ PASSED")
    
    # Test Case 3: No PO or invoice, but has filename with number
    print("\n3. Testing extraction from filename:")
    test_data_3 = {
        "attachments": [
            {"filename": "155987.pdf"}
        ]
    }
    result_3 = extract_document_number(test_data_3)
    print(f"Result: {result_3}")
    assert result_3["po_number"] == "155987", "Failed to extract from filename"
    print("✅ PASSED")
    
    # Test Case 4: No PO, invoice, or filename - should generate fallback
    print("\n4. Testing final fallback generation:")
    test_data_4 = {
        "email_id": "test_email_123456",
        "subject": "Test email without numbers"
    }
    result_4 = extract_document_number(test_data_4)
    print(f"Result: {result_4}")
    assert result_4["po_number"] != "", "Failed to generate fallback PO number"
    assert len(result_4["po_number"]) >= 4, "Generated PO number too short"
    print("✅ PASSED")
    
    # Test Case 5: PO number in email subject
    print("\n5. Testing PO extraction from email subject:")
    test_data_5 = {
        "subject": "Purchase Order PO-987654 for supplies",
        "analysis": {}
    }
    result_5 = extract_document_number(test_data_5)
    print(f"Result: {result_5}")
    assert "987654" in result_5["po_number"], "Failed to extract PO from subject"
    print("✅ PASSED")
    
    # Test Case 6: Complex scenario with multiple sources
    print("\n6. Testing complex scenario with multiple sources:")
    test_data_6 = {
        "subject": "Order confirmation for PO-111111",
        "analysis": {
            "document_info": {
                "invoice_number": "INV222222"
            },
            "items": [
                {"item_number": "333333"}
            ]
        },
        "attachments": [
            {"filename": "444444.pdf"}
        ]
    }
    result_6 = extract_document_number(test_data_6)
    print(f"Result: {result_6}")
    # Should prioritize PO from subject over other sources
    assert "111111" in result_6["po_number"], "Failed to prioritize PO from subject"
    assert result_6["invoice_number"] == "INV222222", "Failed to extract invoice number"
    print("✅ PASSED")
    
    print("\n" + "=" * 60)
    print("ALL TESTS PASSED! ✅")
    print("PO number extraction and fallback logic working correctly.")
    print("=" * 60)

def test_webhook_payload_structure():
    """Test webhook payload preparation"""
    
    print("\n" + "=" * 60)
    print("TESTING WEBHOOK PAYLOAD STRUCTURE")
    print("=" * 60)
    
    from backend.routers.webhook_sender import prepare_webhook_payload
    
    # Test with minimal data
    test_data = {
        "email_id": "test_123",
        "subject": "Test PO Email",
        "from": "<EMAIL>",
        "analysis": {
            "document_info": {
                "invoice_number": "INV456789"
            },
            "financial_details": {
                "total_amount": "1000.00"
            },
            "items": [
                {
                    "product_name": "Test Item",
                    "quantity": 2,
                    "unit_price": 500.00
                }
            ]
        }
    }
    
    try:
        payload = prepare_webhook_payload(test_data)
        print(f"Payload prepared successfully!")
        print(f"PO Number: {payload['document_info']['po_number']}")
        print(f"Invoice Number: {payload['document_info']['invoice_number']}")
        print(f"Total Amount: {payload['financial_details']['total_amount']}")
        print(f"Items Count: {len(payload['items'])}")
        
        # Verify PO number fallback worked
        assert payload['document_info']['po_number'] == "INV456789", "PO fallback failed"
        print("✅ WEBHOOK PAYLOAD TEST PASSED")
        
    except Exception as e:
        print(f"❌ WEBHOOK PAYLOAD TEST FAILED: {str(e)}")
        raise

if __name__ == "__main__":
    try:
        test_po_number_extraction()
        test_webhook_payload_structure()
        print("\n🎉 ALL TESTS COMPLETED SUCCESSFULLY!")
        print("Your email processing system is working correctly with:")
        print("- Comprehensive PO number detection")
        print("- Robust fallback to invoice numbers")
        print("- Guaranteed PO number population")
        print("- Proper webhook payload structure")
        
    except Exception as e:
        print(f"\n❌ TEST FAILED: {str(e)}")
        sys.exit(1)
