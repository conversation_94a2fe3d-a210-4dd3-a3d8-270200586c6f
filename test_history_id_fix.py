#!/usr/bin/env python3
"""
Test script to verify the history ID management fix
"""

import requests
import json
import base64
import logging
import time
from datetime import datetime

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger("history_id_test")

# Configuration
PUBSUB_HANDLER_URL = "http://localhost:8001"  # Adjust if different
TEST_EMAIL = "<EMAIL>"  # Use your actual email
TEST_HISTORY_ID = "71288"  # Use a test history ID

def create_test_pubsub_message(email_address: str, history_id: str):
    """Create a test Pub/Sub message"""
    notification_data = {
        "emailAddress": email_address,
        "historyId": history_id
    }
    
    # Encode the notification data
    encoded_data = base64.b64encode(json.dumps(notification_data).encode('utf-8')).decode('utf-8')
    
    return {
        "message": {
            "data": encoded_data,
            "messageId": f"test-message-{int(time.time())}",
            "publishTime": datetime.now().isoformat()
        }
    }

def test_history_id_management():
    """Test the history ID management fix"""
    try:
        logger.info("🧪 Testing History ID Management Fix")
        logger.info("=" * 50)
        
        # Test 1: Send notification with current history ID
        logger.info(f"📧 Test 1: Sending notification for {TEST_EMAIL} with historyId {TEST_HISTORY_ID}")
        
        test_message = create_test_pubsub_message(TEST_EMAIL, TEST_HISTORY_ID)
        
        response = requests.post(
            f"{PUBSUB_HANDLER_URL}/gmail-webhook",
            json=test_message,
            timeout=30
        )
        
        if response.status_code == 200:
            result = response.json()
            logger.info(f"✅ Test 1 Response: {result.get('message', 'No message')}")
            
            # Check if emails were processed
            emails_processed = result.get('emails_processed', 0)
            logger.info(f"📊 Emails processed: {emails_processed}")
            
            if emails_processed > 0:
                logger.info("✅ SUCCESS: Emails were processed despite matching historyId!")
            else:
                logger.warning("⚠️  No emails processed - this might be expected if no new emails")
                
        else:
            logger.error(f"❌ Test 1 failed: {response.status_code} - {response.text}")
            return False
        
        # Test 2: Check health endpoint
        logger.info("\n📊 Test 2: Checking service health and metrics")
        
        health_response = requests.get(f"{PUBSUB_HANDLER_URL}/health", timeout=10)
        
        if health_response.status_code == 200:
            health_data = health_response.json()
            metrics = health_data.get('metrics', {})
            
            logger.info(f"✅ Service Status: {health_data.get('status', 'unknown')}")
            logger.info(f"📈 Notifications received: {metrics.get('notifications_received', 0)}")
            logger.info(f"📈 Notifications processed: {metrics.get('notifications_processed', 0)}")
            logger.info(f"📈 Emails triggered: {metrics.get('emails_triggered', 0)}")
            
        else:
            logger.warning(f"⚠️  Health check failed: {health_response.status_code}")
        
        # Test 3: Test with different history ID
        logger.info(f"\n📧 Test 3: Testing with different historyId")
        
        different_history_id = str(int(TEST_HISTORY_ID) + 1)
        test_message_2 = create_test_pubsub_message(TEST_EMAIL, different_history_id)
        
        response_2 = requests.post(
            f"{PUBSUB_HANDLER_URL}/gmail-webhook",
            json=test_message_2,
            timeout=30
        )
        
        if response_2.status_code == 200:
            result_2 = response_2.json()
            logger.info(f"✅ Test 3 Response: {result_2.get('message', 'No message')}")
            
            emails_processed_2 = result_2.get('emails_processed', 0)
            logger.info(f"📊 Emails processed: {emails_processed_2}")
            
        else:
            logger.error(f"❌ Test 3 failed: {response_2.status_code} - {response_2.text}")
        
        logger.info("\n" + "=" * 50)
        logger.info("🎉 History ID Management Test Complete!")
        logger.info("Check the logs above to see if the fix is working correctly.")
        logger.info("Key indicators:")
        logger.info("  ✅ Emails processed even when historyId matches current")
        logger.info("  ✅ History ID gets updated in Firestore after processing")
        logger.info("  ✅ Fallback to recent emails works when needed")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Test error: {str(e)}")
        return False

if __name__ == "__main__":
    success = test_history_id_management()
    if success:
        print("\n🎉 Test completed successfully!")
    else:
        print("\n❌ Test failed!")
        exit(1)
