#!/usr/bin/env python3
"""
Test webhook to verify the Pub/Sub -> Email Analysis fix
"""

import requests
import json
import base64
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger("webhook_test")

def create_test_pubsub_message(email_address: str, history_id: str):
    """Create a test Pub/Sub message"""
    notification_data = {
        "emailAddress": email_address,
        "historyId": history_id
    }
    
    # Encode the notification data
    encoded_data = base64.b64encode(json.dumps(notification_data).encode('utf-8')).decode('utf-8')
    
    return {
        "message": {
            "data": encoded_data,
            "messageId": "test-message-id",
            "publishTime": "2023-01-01T00:00:00.000Z"
        }
    }

def test_webhook(url: str, email_address: str, history_id: str):
    """Test the webhook with a sample notification"""
    try:
        logger.info(f"Testing webhook at {url}")
        logger.info(f"Email: {email_address}, HistoryId: {history_id}")
        
        # Create test message
        test_message = create_test_pubsub_message(email_address, history_id)
        
        # Send to webhook
        response = requests.post(
            url,
            json=test_message,
            headers={'Content-Type': 'application/json'},
            timeout=30
        )
        
        logger.info(f"Response Status: {response.status_code}")
        logger.info(f"Response Body: {response.text}")
        
        if response.status_code == 200:
            logger.info("✅ Webhook test successful!")
            return True
        else:
            logger.error(f"❌ Webhook test failed: {response.status_code}")
            return False
            
    except Exception as e:
        logger.error(f"❌ Webhook test error: {str(e)}")
        return False

if __name__ == "__main__":
    # Test with your actual email and a recent history ID
    # Replace these with your actual values
    EMAIL_ADDRESS = "<EMAIL>"  # Replace with your email
    HISTORY_ID = "187254"  # Use a recent history ID from your logs
    
    # Test integrated main webhook
    INTEGRATED_WEBHOOK_URL = "http://localhost:8001/gmail-webhook"
    
    logger.info("🧪 Testing webhook fix...")
    
    if test_webhook(INTEGRATED_WEBHOOK_URL, EMAIL_ADDRESS, HISTORY_ID):
        logger.info("✅ Webhook test passed!")
    else:
        logger.error("❌ Webhook test failed!")
