# Cloud Run service configuration for Pub/Sub Handler
apiVersion: serving.knative.dev/v1
kind: Service
metadata:
  name: email-analyzer-pubsub
  annotations:
    run.googleapis.com/ingress: internal
    run.googleapis.com/execution-environment: gen2
spec:
  template:
    metadata:
      annotations:
        # Scaling configuration
        autoscaling.knative.dev/minScale: "0"
        autoscaling.knative.dev/maxScale: "5"
        
        # Resource allocation
        run.googleapis.com/memory: "512Mi"
        run.googleapis.com/cpu: "1"
        
        # Timeout configuration
        run.googleapis.com/timeout: "60s"
        
        # Concurrency
        run.googleapis.com/execution-environment: gen2
        
    spec:
      # Container concurrency
      containerConcurrency: 50
      
      # Timeout for requests
      timeoutSeconds: 60
      
      containers:
      - name: email-analyzer-pubsub
        image: gcr.io/PROJECT_ID/email-analyzer-pubsub:latest
        
        ports:
        - name: http1
          containerPort: 8080
          
        env:
        # Basic configuration
        - name: PORT
          value: "8080"
        - name: ENVIRONMENT
          value: "production"
        - name: WORKERS
          value: "1"
        - name: HOST
          value: "0.0.0.0"
          
        # Google Cloud settings
        - name: GOOGLE_CLOUD_PROJECT
          value: "PROJECT_ID"
        - name: PUBSUB_TOPIC
          value: "projects/PROJECT_ID/topics/email-notifications"
          
        # Main FastAPI URL (will be updated after backend deployment)
        - name: MAIN_FASTAPI_URL
          value: "https://emailbot-524036921514.us-central1.run.app"
          
        # Performance and reliability settings
        - name: MAX_RETRIES
          value: "3"
        - name: RETRY_DELAY
          value: "1.0"
        - name: REQUEST_TIMEOUT
          value: "30"
        - name: MAX_EMAILS_PER_NOTIFICATION
          value: "10"
          
        # Rate limiting
        - name: RATE_LIMIT_WINDOW
          value: "60"
        - name: MAX_REQUESTS_PER_WINDOW
          value: "100"
        - name: MAX_CONCURRENT_PROCESSING
          value: "10"
          
        # Circuit breaker settings
        - name: CIRCUIT_BREAKER_FAILURE_THRESHOLD
          value: "5"
        - name: CIRCUIT_BREAKER_RECOVERY_TIMEOUT
          value: "60"
          
        # Health check settings
        - name: HEALTH_CHECK_INTERVAL
          value: "30"
        - name: FIRESTORE_HEALTH_CHECK_TIMEOUT
          value: "5"
          
        # Logging
        - name: LOG_LEVEL
          value: "INFO"
          
        # Secrets from Secret Manager
        - name: SERVICE_TOKEN
          valueFrom:
            secretKeyRef:
              name: email-analyzer-service-token
              key: latest
              
        - name: GOOGLE_APPLICATION_CREDENTIALS
          valueFrom:
            secretKeyRef:
              name: firebase-credentials
              key: latest
        
        # Resource requests and limits
        resources:
          requests:
            memory: "256Mi"
            cpu: "250m"
          limits:
            memory: "512Mi"
            cpu: "1000m"
            
        # Health checks
        livenessProbe:
          httpGet:
            path: /health
            port: 8080
          initialDelaySeconds: 30
          periodSeconds: 30
          timeoutSeconds: 10
          failureThreshold: 3
          
        readinessProbe:
          httpGet:
            path: /health
            port: 8080
          initialDelaySeconds: 10
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 3
          
        # Startup probe for cold starts
        startupProbe:
          httpGet:
            path: /health
            port: 8080
          initialDelaySeconds: 0
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 30
          
      # Service account for authentication
      serviceAccountName: email-analyzer-pubsub-sa
      
  traffic:
  - percent: 100
    latestRevision: true
