#!/bin/bash

# Fix Pub/Sub Backend URL Configuration
# This script updates the MAIN_FASTAPI_URL environment variable in the Pub/Sub service

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Configuration
PROJECT_ID="ai-email-bot-455814"
REGION="us-central1"
PUBSUB_SERVICE="email-analyzer-pubsub"
BACKEND_URL="https://emailbot-524036921514.us-central1.run.app"

echo -e "${BLUE}🔧 Fixing Pub/Sub Backend URL Configuration${NC}"
echo -e "${BLUE}Project: $PROJECT_ID${NC}"
echo -e "${BLUE}Region: $REGION${NC}"
echo -e "${BLUE}Backend URL: $BACKEND_URL${NC}"
echo ""

# Update the Pub/Sub service environment variable
echo -e "${BLUE}📋 Updating MAIN_FASTAPI_URL environment variable...${NC}"

gcloud run services update $PUBSUB_SERVICE \
    --region=$REGION \
    --project=$PROJECT_ID \
    --set-env-vars="MAIN_FASTAPI_URL=$BACKEND_URL" \
    --quiet

if [ $? -eq 0 ]; then
    echo -e "${GREEN}✅ Successfully updated MAIN_FASTAPI_URL${NC}"
else
    echo -e "${RED}❌ Failed to update MAIN_FASTAPI_URL${NC}"
    exit 1
fi

# Wait for deployment to complete
echo -e "${BLUE}⏳ Waiting for deployment to complete...${NC}"
sleep 10

# Verify the update
echo -e "${BLUE}🔍 Verifying the configuration...${NC}"

# Get the current environment variables
ENV_VARS=$(gcloud run services describe $PUBSUB_SERVICE \
    --region=$REGION \
    --project=$PROJECT_ID \
    --format='value(spec.template.spec.containers[0].env[].name,spec.template.spec.containers[0].env[].value)' | \
    grep -A1 "MAIN_FASTAPI_URL" || true)

if echo "$ENV_VARS" | grep -q "$BACKEND_URL"; then
    echo -e "${GREEN}✅ Configuration verified successfully${NC}"
    echo -e "${GREEN}MAIN_FASTAPI_URL is now set to: $BACKEND_URL${NC}"
else
    echo -e "${YELLOW}⚠️  Configuration verification inconclusive${NC}"
    echo -e "${YELLOW}Please check manually in Cloud Console${NC}"
fi

# Test the health endpoint
echo -e "${BLUE}🏥 Testing Pub/Sub health endpoint...${NC}"
PUBSUB_URL="https://pubsub-524036921514.us-central1.run.app"

TOKEN=$(gcloud auth print-identity-token 2>/dev/null || echo "")
if [ -n "$TOKEN" ]; then
    if curl -f -s -H "Authorization: Bearer $TOKEN" "$PUBSUB_URL/health" > /dev/null; then
        echo -e "${GREEN}✅ Pub/Sub health check passed${NC}"
    else
        echo -e "${YELLOW}⚠️  Pub/Sub health check failed (may need authentication)${NC}"
    fi
else
    echo -e "${YELLOW}⚠️  Could not get auth token for health check${NC}"
fi

echo ""
echo -e "${GREEN}🎉 Fix Complete!${NC}"
echo ""
echo -e "${BLUE}📋 Next Steps:${NC}"
echo "1. Your Pub/Sub service should now correctly call your backend"
echo "2. Send a test email to trigger the notification"
echo "3. Check the logs to verify email analysis is working"
echo ""
echo -e "${BLUE}📊 Service URLs:${NC}"
echo "Backend: $BACKEND_URL"
echo "Pub/Sub: $PUBSUB_URL"
echo ""
echo -e "${BLUE}🔍 To check logs:${NC}"
echo "gcloud logs read --project=$PROJECT_ID --filter='resource.type=\"cloud_run_revision\" AND resource.labels.service_name=\"$PUBSUB_SERVICE\"' --limit=50"
