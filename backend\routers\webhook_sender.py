"""
Updated Webhook Sender Module with Clean JSON Structure

This module handles sending email analysis data and attachments to a webhook
using the new clean, non-duplicate JSON structure.
"""

import requests
import logging
import json
import base64
import tempfile
import os
import re
import time
import traceback
from typing import Dict, Any, List, Optional, Union
from firebase_admin import storage
from datetime import datetime
from firebase_admin import firestore

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger("webhook_sender")
logger.setLevel(logging.DEBUG)
console_handler = logging.StreamHandler()
console_handler.setLevel(logging.DEBUG)
formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
console_handler.setFormatter(formatter)
logger.addHandler(console_handler)
logger.propagate = True

# Webhook URL
WEBHOOK_URL = "https://spectrum.omsflow.com/api/po_webhook.php"

# Deduplication tracker
webhook_call_tracker = {}

def get_structured_address(address_data: Union[str, Dict[str, Any], None]) -> Dict[str, str]:
    """
    Convert address data to structured format.
    Handles both legacy string addresses and new structured address objects.
    
    Args:
        address_data: Can be a string, dict, or None
        
    Returns:
        Dict with structured address fields
    """
    # Default structured address
    structured_address = {
        "address_line1": "",
        "address_line2": "",
        "city": "",
        "state": "",
        "country": "",
        "zip_code": ""
    }
    
    if not address_data:
        return structured_address
    
    # If already structured (dict), return it with defaults filled
    if isinstance(address_data, dict):
        structured_address.update({
            "address_line1": address_data.get('address_line1', ''),
            "address_line2": address_data.get('address_line2', ''),
            "city": address_data.get('city', ''),
            "state": address_data.get('state', ''),
            "country": address_data.get('country', ''),
            "zip_code": address_data.get('zip_code', '')
        })
        return structured_address
    
    # If it's a string, try to parse it into structured format
    if isinstance(address_data, str) and address_data.strip():
        try:
            # Basic parsing logic for common address formats
            # This is a fallback for legacy string addresses
            address_parts = [part.strip() for part in address_data.split(',')]
            
            if len(address_parts) >= 1:
                structured_address["address_line1"] = address_parts[0]
            
            if len(address_parts) >= 2:
                # Try to identify city, state, zip pattern
                last_part = address_parts[-1].strip()
                
                # Check if last part contains zip code (numbers)
                import re
                zip_match = re.search(r'\b\d{5}(?:-\d{4})?\b', last_part)
                if zip_match:
                    zip_code = zip_match.group()
                    structured_address["zip_code"] = zip_code
                    
                    # Remove zip from last part to get state
                    state_part = last_part.replace(zip_code, '').strip()
                    if state_part:
                        structured_address["state"] = state_part
                
                # Second to last part might be city
                if len(address_parts) >= 3:
                    structured_address["city"] = address_parts[-2].strip()
                elif len(address_parts) == 2 and not structured_address["state"]:
                    # Only two parts, second might be city+state+zip
                    structured_address["city"] = address_parts[-1].strip()
                
                # Middle parts might be address_line2
                if len(address_parts) >= 3:
                    middle_parts = address_parts[1:-2] if len(address_parts) > 3 else [address_parts[1]]
                    if middle_parts and any(part.strip() for part in middle_parts):
                        structured_address["address_line2"] = ', '.join(part.strip() for part in middle_parts if part.strip())
        
        except Exception as e:
            logger.warning(f"Error parsing address string '{address_data}': {str(e)}")
            # Fallback: put entire string in address_line1
            structured_address["address_line1"] = address_data.strip()
    
    return structured_address

def safe_str_conversion(value: Any, default: str = "0.00") -> str:
    """
    Safely convert a value to string, handling None values appropriately.
    For numerical fields, returns default value. For text fields, returns empty string.
    """
    if value is None:
        return default
    elif value == "":
        return default if default != "" else ""
    elif isinstance(value, str) and value.lower() in ['none', 'null']:
        return default
    else:
        return str(value)

def safe_float_conversion(value: Any, default: float = 0.0) -> float:
    """
    Safely convert a value to float, handling None, empty strings, and invalid values.
    
    Args:
        value: The value to convert to float
        default: Default value to return if conversion fails
        
    Returns:
        Float value or default
    """
    if value is None or value == "":
        return default
    
    try:
        return float(value)
    except (ValueError, TypeError):
        return default

def ensure_no_null_values(data: Any) -> Any:
    """
    Recursively convert all None/null values to empty strings in the data structure.
    This ensures webhook payloads never contain null values.
    
    Args:
        data: Any data structure (dict, list, primitive values)
        
    Returns:
        Data structure with all None values replaced with empty strings
    """
    if data is None:
        return ""
    elif isinstance(data, str) and data.lower() in ['none', 'null']:
        # Also convert string representations of None/null
        return ""
    elif isinstance(data, dict):
        return {key: ensure_no_null_values(value) for key, value in data.items()}
    elif isinstance(data, list):
        return [ensure_no_null_values(item) for item in data]
    else:
        return data

def safe_convert_firestore_value(value: Any) -> Any:
    """
    Convert Firestore special values (like Sentinel) to JSON-serializable values
    """
    if hasattr(value, '__class__') and 'Sentinel' in str(value.__class__):
        # Convert Firestore SERVER_TIMESTAMP to current datetime
        return datetime.now().isoformat()
    elif isinstance(value, dict):
        return {k: safe_convert_firestore_value(v) for k, v in value.items()}
    elif isinstance(value, list):
        return [safe_convert_firestore_value(item) for item in value]
    else:
        return value

def format_date_to_string(date_value: Any) -> str:
    """
    Convert various date formats to YYYY-MM-DD string format
    """
    if not date_value:
        return ""

    try:
        # Handle Firestore Sentinel objects
        if hasattr(date_value, '__class__') and 'Sentinel' in str(date_value.__class__):
            # Use current date for server timestamps
            return datetime.now().strftime('%Y-%m-%d')

        if isinstance(date_value, str):
            date_value = date_value.strip()
            
            # Check if already in YYYY-MM-DD format
            if len(date_value) == 10 and date_value.count('-') == 2:
                try:
                    datetime.strptime(date_value, '%Y-%m-%d')
                    return date_value
                except ValueError:
                    pass

            # Handle email date format like "Tue, 24 Jun 2025 19:49:52 -0400"
            if ',' in date_value and ('-' in date_value or '+' in date_value):
                try:
                    # Remove day name and timezone for parsing
                    if '+' in date_value:
                        clean_date = date_value.split(',')[1].strip().split('+')[0].strip()
                    else:
                        clean_date = date_value.split(',')[1].strip().split('-')[0].strip()
                    parsed_date = datetime.strptime(clean_date, '%d %b %Y %H:%M:%S')
                    return parsed_date.strftime('%Y-%m-%d')
                except (ValueError, IndexError):
                    pass

            # Handle format like "May 27, 2025"
            if ',' in date_value:
                try:
                    parsed_date = datetime.strptime(date_value, '%B %d, %Y')
                    return parsed_date.strftime('%Y-%m-%d')
                except ValueError:
                    try:
                        parsed_date = datetime.strptime(date_value, '%b %d, %Y')
                        return parsed_date.strftime('%Y-%m-%d')
                    except ValueError:
                        pass

            # Try to parse common date formats
            date_formats = [
                '%Y-%m-%d', '%Y/%m/%d', '%m/%d/%Y', '%d/%m/%Y', 
                '%Y-%m-%dT%H:%M:%S', '%Y-%m-%dT%H:%M:%S.%f',
                '%B %d %Y', '%b %d %Y', '%d %B %Y', '%d %b %Y',
                '%Y-%m-%d %H:%M:%S', '%m-%d-%Y', '%d-%m-%Y'
            ]
            
            for fmt in date_formats:
                try:
                    parsed_date = datetime.strptime(date_value, fmt)
                    return parsed_date.strftime('%Y-%m-%d')
                except ValueError:
                    continue

        elif isinstance(date_value, datetime):
            return date_value.strftime('%Y-%m-%d')

        elif isinstance(date_value, (int, float)):
            return datetime.fromtimestamp(date_value).strftime('%Y-%m-%d')

        print(f"[DATE FORMAT] Could not parse date: {date_value} (type: {type(date_value)})")
        return ""

    except Exception as e:
        print(f"[DATE FORMAT ERROR] Error formatting date {date_value}: {str(e)}")
        return ""

def convert_attachment_to_blob(attachment_data: bytes) -> str:
    """Convert attachment data to base64 encoded string"""
    try:
        blob_data = base64.b64encode(attachment_data).decode('utf-8')
        print(f"[WEBHOOK DATA] Converted attachment to blob, size: {len(blob_data)} characters")
        return blob_data
    except Exception as e:
        logger.error(f"Error converting attachment to blob: {str(e)}")
        return ""

def download_attachment_from_url(url: str) -> Optional[bytes]:
    """Download attachment data from a URL"""
    try:
        print(f"[WEBHOOK DATA] Downloading attachment from URL: {url}")
        response = requests.get(url, timeout=30)
        if response.status_code == 200:
            print(f"[WEBHOOK DATA] Successfully downloaded attachment, size: {len(response.content)} bytes")
            return response.content
        else:
            logger.error(f"Failed to download attachment from URL: {url}, Status code: {response.status_code}")
            return None
    except Exception as e:
        logger.error(f"Error downloading attachment from URL: {str(e)}")
        return None

def extract_document_number(analysis_data: Dict[str, Any]) -> Dict[str, str]:
    """
    Extract document numbers (PO, Invoice, Order) from analysis data
    Enhanced with comprehensive fallback logic to ensure PO number is always populated
    """
    document_numbers = {
        "document_number": "",
        "po_number": "",
        "invoice_number": ""
    }

    try:
        print(f"[DOCUMENT EXTRACTION] Starting enhanced extraction from analysis_data keys: {list(analysis_data.keys())}")

        # Check various locations for document numbers
        if 'analysis' in analysis_data:
            analysis = analysis_data['analysis']
            doc_info = analysis.get('document_info', {})
            items = analysis.get('items', [])
            attachment_analysis = analysis.get('attachment_analysis', [])
            email_analysis = analysis.get('email_analysis', {})
        else:
            doc_info = analysis_data.get('document_info', {})
            items = analysis_data.get('items', [])
            attachment_analysis = analysis_data.get('attachment_analysis', [])
            email_analysis = analysis_data.get('email_analysis', {})

        print(f"[DOCUMENT EXTRACTION] doc_info: {doc_info}")
        print(f"[DOCUMENT EXTRACTION] items count: {len(items)}")
        print(f"[DOCUMENT EXTRACTION] attachment_analysis count: {len(attachment_analysis)}")

        # STEP 1: Extract PO number from multiple sources
        po_sources = [
            # Primary document info sources
            doc_info.get('po_number'),
            doc_info.get('order_number'),
            doc_info.get('purchase_order_number'),
            doc_info.get('document_number'),
            # Top-level analysis data
            analysis_data.get('po_number'),
            analysis_data.get('order_number'),
            analysis_data.get('purchase_order_number'),
            analysis_data.get('document_number'),
        ]

        # Check email subject and body for PO patterns
        email_subject = analysis_data.get('subject', '') or email_analysis.get('subject', '')
        email_body = ''
        if isinstance(analysis_data.get('body'), dict):
            email_body = analysis_data['body'].get('text', '') or analysis_data['body'].get('html', '')
        elif isinstance(analysis_data.get('body'), str):
            email_body = analysis_data['body']

        combined_text = f"{email_subject} {email_body}"

        # Enhanced PO pattern matching
        if combined_text:
            import re
            po_patterns = [
                r'(?:PO|P\.O\.?|Purchase\s+Order)\s*[#:\-]?\s*(\d{4,12})',
                r'(?:Order|Order\s+Number|Order\s+No\.?)\s*[#:\-]?\s*(\d{4,12})',
                r'(?:Reference|Ref\.?)\s*[#:\-]?\s*(\d{6,12})',
                r'(\d{6,10})'  # Any 6-10 digit number might be a PO
            ]

            for pattern in po_patterns:
                matches = re.findall(pattern, combined_text, re.IGNORECASE)
                for match in matches:
                    if match.isdigit() and 4 <= len(match) <= 12:
                        po_sources.append(match)
                        print(f"[DOCUMENT EXTRACTION] Found potential PO in email content: {match}")

        # Check attachment filenames for PO numbers (like "155987.pdf")
        attachments = analysis_data.get('attachments', [])
        for attachment in attachments:
            filename = attachment.get('filename', '')
            if filename:
                # Extract number from filename (remove extension)
                name_without_ext = filename.split('.')[0]
                # Check if it's a valid PO number (numeric and reasonable length)
                if name_without_ext.isdigit() and 4 <= len(name_without_ext) <= 12:
                    po_sources.append(name_without_ext)
                    print(f"[DOCUMENT EXTRACTION] Found potential PO number in filename: {name_without_ext}")

        # Check items for PO/Order numbers
        for item in items:
            if isinstance(item, dict):
                item_number = item.get('item_number') or item.get('sku') or item.get('part_number')
                if item_number and str(item_number).strip():
                    # If item number looks like a PO (longer than 3 digits), include it
                    if str(item_number).strip().isdigit() and len(str(item_number).strip()) > 3:
                        po_sources.append(str(item_number).strip())

        # Check attachment analysis for extracted numbers
        for att_analysis in attachment_analysis:
            if isinstance(att_analysis, dict):
                att_po = (att_analysis.get('po_number') or
                         att_analysis.get('order_number') or
                         att_analysis.get('purchase_order_number') or
                         att_analysis.get('document_number'))
                if att_po:
                    po_sources.append(str(att_po).strip())

        # Check summary and email analysis for PO numbers
        summary_text = analysis_data.get('summary', '') or email_analysis.get('summary', '')
        if summary_text:
            import re
            # Enhanced PO patterns for summary text
            po_patterns = [
                r'(?:PO|P\.O\.?|Purchase\s+Order)\s*[#:\-]?\s*(\d{4,12})',
                r'(?:Order|Order\s+Number|Order\s+No\.?)\s*[#:\-]?\s*(\d{4,12})',
                r'(?:Reference|Ref\.?)\s*[#:\-]?\s*(\d{6,12})',
                r'(\d{6,10})'  # Any 6-10 digit number might be a PO
            ]

            for pattern in po_patterns:
                matches = re.findall(pattern, summary_text, re.IGNORECASE)
                for match in matches:
                    if match.isdigit() and 4 <= len(match) <= 12:
                        po_sources.append(match)
                        print(f"[DOCUMENT EXTRACTION] Found potential PO in summary: {match}")

        print(f"[DOCUMENT EXTRACTION] PO sources to check: {po_sources}")

        # Find the best PO number
        for po in po_sources:
            if po and str(po).strip() and str(po).strip().lower() not in ['null', 'none', '', '0', 'na', 'n/a']:
                document_numbers["po_number"] = str(po).strip()
                if not document_numbers["document_number"]:
                    document_numbers["document_number"] = str(po).strip()
                print(f"[DOCUMENT EXTRACTION] Found PO number: {str(po).strip()}")
                break

        # STEP 2: Extract Invoice number from multiple sources
        invoice_sources = [
            doc_info.get('invoice_number'),
            doc_info.get('bill_number'),
            analysis_data.get('invoice_number'),
            analysis_data.get('bill_number')
        ]

        # Check attachment filenames for invoice numbers
        for attachment in attachments:
            filename = attachment.get('filename', '')
            if filename:
                # Check for invoice-related keywords in filename
                if any(keyword in filename.lower() for keyword in ['invoice', 'inv', 'bill', 'receipt']):
                    name_without_ext = filename.split('.')[0]
                    # Extract numbers from invoice filenames
                    import re
                    numbers = re.findall(r'\d{4,12}', name_without_ext)
                    for num in numbers:
                        if 4 <= len(num) <= 12:
                            invoice_sources.append(num)
                            print(f"[DOCUMENT EXTRACTION] Found potential invoice number in filename: {num}")

        # Check email content for invoice numbers
        if combined_text:
            import re
            invoice_patterns = [
                r'(?:Invoice|Inv\.?)\s*[#:\-]?\s*(\d{4,12})',
                r'(?:Bill|Receipt)\s*[#:\-]?\s*(\d{4,12})',
                r'(?:Payment|Pay)\s*[#:\-]?\s*(\d{4,12})'
            ]

            for pattern in invoice_patterns:
                matches = re.findall(pattern, combined_text, re.IGNORECASE)
                for match in matches:
                    if match.isdigit() and 4 <= len(match) <= 12:
                        invoice_sources.append(match)
                        print(f"[DOCUMENT EXTRACTION] Found potential invoice in email content: {match}")

        # Check summary for invoice numbers
        if summary_text:
            import re
            invoice_patterns = [
                r'(?:Invoice|Inv\.?)\s*[#:\-]?\s*(\d{4,12})',
                r'(?:Bill|Receipt)\s*[#:\-]?\s*(\d{4,12})',
                r'(?:Payment|Pay)\s*[#:\-]?\s*(\d{4,12})'
            ]

            for pattern in invoice_patterns:
                matches = re.findall(pattern, summary_text, re.IGNORECASE)
                for match in matches:
                    if match.isdigit() and 4 <= len(match) <= 12:
                        invoice_sources.append(match)
                        print(f"[DOCUMENT EXTRACTION] Found potential invoice in summary: {match}")

        # Find the best invoice number
        for invoice in invoice_sources:
            if invoice and str(invoice).strip() and str(invoice).strip().lower() not in ['null', 'none', '', '0', 'na', 'n/a']:
                document_numbers["invoice_number"] = str(invoice).strip()
                if not document_numbers["document_number"]:
                    document_numbers["document_number"] = str(invoice).strip()
                print(f"[DOCUMENT EXTRACTION] Found invoice number: {str(invoice).strip()}")
                break

        # STEP 3: CRITICAL FALLBACK LOGIC - Ensure PO number is always populated
        if not document_numbers["po_number"]:
            print(f"[DOCUMENT EXTRACTION] No PO number found, applying fallback logic...")

            # First fallback: Use invoice number as PO number
            if document_numbers["invoice_number"]:
                document_numbers["po_number"] = document_numbers["invoice_number"]
                print(f"[DOCUMENT EXTRACTION] FALLBACK 1: Using invoice number as PO: {document_numbers['po_number']}")

            # Second fallback: Use document number as PO number
            elif document_numbers["document_number"]:
                document_numbers["po_number"] = document_numbers["document_number"]
                print(f"[DOCUMENT EXTRACTION] FALLBACK 2: Using document number as PO: {document_numbers['po_number']}")

            # Third fallback: Extract any number from email subject
            elif email_subject:
                import re
                numbers = re.findall(r'\d{4,12}', email_subject)
                if numbers:
                    document_numbers["po_number"] = numbers[0]
                    print(f"[DOCUMENT EXTRACTION] FALLBACK 3: Using number from subject as PO: {document_numbers['po_number']}")
                else:
                    # No numbers in subject, generate fallback
                    import time
                    fallback_po = f"{int(time.time())}"[-8:]  # Last 8 digits
                    document_numbers["po_number"] = fallback_po
                    print(f"[DOCUMENT EXTRACTION] FALLBACK 3B: Generated timestamp-based PO: {document_numbers['po_number']}")

            # Fourth fallback: Use email ID or timestamp-based number
            else:
                email_id = analysis_data.get('email_id', analysis_data.get('id', ''))
                if email_id:
                    # Extract numbers from email ID
                    import re
                    numbers = re.findall(r'\d+', str(email_id))
                    if numbers:
                        # Use the longest number found
                        longest_number = max(numbers, key=len)
                        if len(longest_number) >= 4:
                            document_numbers["po_number"] = longest_number
                            print(f"[DOCUMENT EXTRACTION] FALLBACK 4: Using email ID number as PO: {document_numbers['po_number']}")
                        else:
                            # Generate a fallback PO based on timestamp
                            import time
                            fallback_po = f"{int(time.time())}"[-8:]  # Last 8 digits
                            document_numbers["po_number"] = fallback_po
                            print(f"[DOCUMENT EXTRACTION] FALLBACK 5: Generated timestamp-based PO: {document_numbers['po_number']}")
                    else:
                        # Generate a fallback PO based on timestamp
                        import time
                        fallback_po = f"{int(time.time())}"[-8:]  # Last 8 digits
                        document_numbers["po_number"] = fallback_po
                        print(f"[DOCUMENT EXTRACTION] FALLBACK 6: Generated timestamp-based PO: {document_numbers['po_number']}")
                else:
                    # Final fallback: Generate a timestamp-based PO
                    import time
                    fallback_po = f"{int(time.time())}"[-8:]  # Last 8 digits
                    document_numbers["po_number"] = fallback_po
                    print(f"[DOCUMENT EXTRACTION] FALLBACK 7: Generated final timestamp-based PO: {document_numbers['po_number']}")

        # Ensure document_number is populated
        if not document_numbers["document_number"]:
            document_numbers["document_number"] = document_numbers["po_number"]
            print(f"[DOCUMENT EXTRACTION] Set document_number to PO: {document_numbers['document_number']}")

        print(f"[DOCUMENT EXTRACTION] Final extracted numbers: {document_numbers}")
        print(f"[DOCUMENT EXTRACTION] SUCCESS: PO number is guaranteed to be populated: {document_numbers['po_number']}")

        return document_numbers

    except Exception as e:
        logger.error(f"Error extracting document numbers: {str(e)}")
        logger.error(f"Traceback: {traceback.format_exc()}")

        # Even in error case, ensure PO number is populated
        if not document_numbers["po_number"]:
            import time
            fallback_po = f"ERR{int(time.time())}"[-8:]  # Last 8 digits with ERR prefix
            document_numbers["po_number"] = fallback_po
            document_numbers["document_number"] = fallback_po
            print(f"[DOCUMENT EXTRACTION] ERROR FALLBACK: Generated error-case PO: {document_numbers['po_number']}")

        return document_numbers

def prepare_webhook_payload(analysis_data: Dict[str, Any]) -> Dict[str, Any]:
    """
    Prepare the payload using the new clean JSON structure
    """
    try:
        print(f"\n[WEBHOOK PREP] Starting payload preparation with clean structure...")
        print(f"[DEBUG] Raw analysis_data keys: {list(analysis_data.keys())}")
        print(f"[DEBUG] email_id: {analysis_data.get('email_id', 'NOT_FOUND')}")
        
        # Only show complete raw data if in debug mode
        if logger.level <= 10:  # DEBUG level
            try:
                import json
                print(f"\n[RAW DATA DEBUG] Complete analysis data:")
                print(json.dumps(analysis_data, indent=2, default=str)[:500] + "..." if len(str(analysis_data)) > 500 else json.dumps(analysis_data, indent=2, default=str))
            except Exception:
                print(f"[RAW DATA DEBUG] Raw data keys: {list(analysis_data.keys())}")
            print(f"[RAW DATA DEBUG] End raw data\n")

        # First, convert all Firestore values to safe JSON values
        safe_analysis_data = safe_convert_firestore_value(analysis_data)

        # Extract nested analysis data
        if 'analysis' in safe_analysis_data:
            analysis = safe_analysis_data['analysis']
            print(f"[DEBUG] Found nested analysis with keys: {list(analysis.keys())}")
            email_analysis = analysis.get('email_analysis', {})
            attachment_analysis = analysis.get('attachment_analysis', [])
            financial_details = analysis.get('financial_details', {})
            document_info = analysis.get('document_info', {})
            parties = analysis.get('parties', {})
            items = analysis.get('items', [])
            dates = analysis.get('dates', {})
            additional_info = analysis.get('additional_info', {})
            order_details = analysis.get('order_details', {})
            payment_info = analysis.get('payment_info', {})
        else:
            print(f"[DEBUG] No nested analysis found, using top-level data")
            email_analysis = safe_analysis_data.get('email_analysis', {})
            attachment_analysis = safe_analysis_data.get('attachment_analysis', [])
            financial_details = safe_analysis_data.get('financial_details', {})
            document_info = safe_analysis_data.get('document_info', {})
            parties = safe_analysis_data.get('parties', {})
            items = safe_analysis_data.get('items', [])
            dates = safe_analysis_data.get('dates', {})
            additional_info = safe_analysis_data.get('additional_info', {})
            order_details = safe_analysis_data.get('order_details', {})
            payment_info = safe_analysis_data.get('payment_info', {})

        # Show key extracted data for debugging (reduced verbosity)
        print(f"[DEBUG] Found {len(items) if isinstance(items, list) else 0} items, {len(parties.keys()) if isinstance(parties, dict) else 0} parties, total: ${financial_details.get('total_amount', '0.00')}")
        
        # Show structure summary for debugging
        if 'analysis' in safe_analysis_data:
            print(f"[STRUCTURE DEBUG] Analysis sections: {list(safe_analysis_data['analysis'].keys())}")

        # Ensure all extracted fields are of the expected type
        email_analysis = email_analysis if isinstance(email_analysis, dict) else {}
        attachment_analysis = attachment_analysis if isinstance(attachment_analysis, list) else []
        financial_details = financial_details if isinstance(financial_details, dict) else {}
        document_info = document_info if isinstance(document_info, dict) else {}
        parties = parties if isinstance(parties, dict) else {}
        items = items if isinstance(items, list) else []
        dates = dates if isinstance(dates, (dict, list)) else {}  # Allow both dict and list
        additional_info = additional_info if isinstance(additional_info, dict) else {}
        order_details = order_details if isinstance(order_details, dict) else {}
        payment_info = payment_info if isinstance(payment_info, dict) else {}

        print(f"[WEBHOOK PREP] Type validation complete - dates type: {type(dates)}, items type: {type(items)}")
        print(f"[DEBUG] After type validation - items: {items}")
        print(f"[DEBUG] After type validation - dates: {dates}")

        # Extract document numbers
        doc_numbers = extract_document_number(safe_analysis_data)

        # Determine document type and category
        category = safe_analysis_data.get('category', email_analysis.get('category', 'other'))
        if category == 'order':
            category = 'purchase_order'

        document_type = document_info.get('type', 'Document')
        if 'order' in category.lower() or 'purchase' in category.lower():
            document_type = 'Purchase Order'
        elif 'invoice' in category.lower():
            document_type = 'Invoice'

        # Build the dates section FIRST - handle both dict and list formats
        dates_section = {
            "document_date": "",
            "due_date": "",
            "ship_date": "",
            "delivery_date": "",
            "payment_date": ""
        }
        
        # Handle different date formats
        if isinstance(dates, dict):
            # Dictionary format with named date fields
            dates_section.update({
                "document_date": format_date_to_string(dates.get('invoice_date') or dates.get('order_date') or dates.get('document_date')),
                "due_date": format_date_to_string(dates.get('due_date')),
                "ship_date": format_date_to_string(dates.get('ship_date')),
                "delivery_date": format_date_to_string(dates.get('delivery_date')),
                "payment_date": format_date_to_string(dates.get('payment_date'))
            })
        elif isinstance(dates, list) and dates:
            # List format - use first date as document date
            dates_section["document_date"] = format_date_to_string(dates[0] if dates else "")
            # If there are more dates, try to assign them to other fields
            if len(dates) > 1:
                dates_section["due_date"] = format_date_to_string(dates[1])
            if len(dates) > 2:
                dates_section["delivery_date"] = format_date_to_string(dates[2])
        
        # Also check key_dates field as fallback
        key_dates = safe_analysis_data.get('key_dates', [])
        if not dates_section["document_date"] and key_dates:
            if isinstance(key_dates, list) and key_dates:
                dates_section["document_date"] = format_date_to_string(key_dates[0])
            elif isinstance(key_dates, str):
                dates_section["document_date"] = format_date_to_string(key_dates)

        # Calculate key_dates_extracted BEFORE building payload
        key_dates_extracted = len([d for d in dates_section.values() if d])

        # Determine email_id - try multiple possible fields
        email_id = (safe_analysis_data.get('email_id') or 
                   safe_analysis_data.get('emailId') or 
                   safe_analysis_data.get('composite_id') or 
                   safe_analysis_data.get('id') or '')
        
        print(f"[DEBUG] Final email_id selected: {email_id}")

        # Now build the complete payload structure
        payload = {
            "company_id": "1006",
            "created_by": "1",
            "email_id": email_id,
            "category": category,
            "analyzed_at": format_date_to_string(safe_analysis_data.get('analyzed_at', datetime.now().isoformat())),

            "document_info": {
                "type": document_type,
                "document_number": doc_numbers["document_number"],
                "po_number": doc_numbers["po_number"],
                "invoice_number": doc_numbers["invoice_number"],
                "reference_numbers": document_info.get('reference_numbers', []),
                "status": document_info.get('status', 'pending')
            },

            "email_metadata": {
                "subject": safe_analysis_data.get('subject', ''),
                "from": safe_analysis_data.get('from', ''),
                "to": safe_analysis_data.get('to', ''),
                "cc": safe_analysis_data.get('cc', ''),
                "timestamp": format_date_to_string(safe_analysis_data.get('date', '')),
                "has_attachments": bool(safe_analysis_data.get('attachments', [])),
                "attachment_count": len(safe_analysis_data.get('attachments', [])),
                "attachment_names": [att.get('filename', '') if isinstance(att, dict) else str(att) for att in safe_analysis_data.get('attachments', [])]
            },

            "parties": {
                "vendor": {
                    "name": parties.get('vendor', {}).get('name', ''),
                    "address": get_structured_address(parties.get('vendor', {}).get('address')),
                    "phone": parties.get('vendor', {}).get('phone', ''),
                    "email": parties.get('vendor', {}).get('email', ''),
                    "tax_id": parties.get('vendor', {}).get('tax_id', '')
                },
                "customer": {
                    "name": parties.get('customer', {}).get('name', ''),
                    "address": get_structured_address(parties.get('customer', {}).get('address')),
                    "phone": parties.get('customer', {}).get('phone', ''),
                    "email": parties.get('customer', {}).get('email', ''),
                    "customer_id": parties.get('customer', {}).get('customer_id', '')
                },
                "ship_to": {
                    "name": parties.get('ship_to', {}).get('name', ''),
                    "address": get_structured_address(parties.get('ship_to', {}).get('address')),
                    "phone": parties.get('ship_to', {}).get('phone', '')
                },
                "bill_to": {
                    "name": parties.get('bill_to', {}).get('name', ''),
                    "address": get_structured_address(parties.get('bill_to', {}).get('address')),
                    "phone": parties.get('bill_to', {}).get('phone', ''),
                    "email": parties.get('bill_to', {}).get('email', '')
                }
            },

            "dates": dates_section,

            # Items will be processed below with amount calculations
            "items": [],

            # Financial details will be calculated after item processing
            "financial_details": {},

            "order_details": {
                "delivery_method": order_details.get('delivery_method', ''),
                "carrier": order_details.get('carrier', ''),
                "tracking_number": order_details.get('tracking_number', ''),
                "shipping_terms": order_details.get('shipping_terms', ''),
                "estimated_delivery": format_date_to_string(order_details.get('estimated_arrival')),
                "special_instructions": order_details.get('delivery_instructions', '')
            },

            "payment_info": {
                "payment_method": payment_info.get('method', ''),
                "payment_terms": payment_info.get('terms', 'Net 30'),
                "early_payment_discount": payment_info.get('early_payment_discount', ''),
                "late_payment_penalty": payment_info.get('late_payment_penalty', '')
            },

            "analysis_results": {
                "summary": safe_analysis_data.get('summary', email_analysis.get('summary', 'No summary generated')),
                "sentiment": email_analysis.get('sentiment', 'neutral'),
                "confidence_score": 0.95,  # Default confidence score
                "key_dates_extracted": key_dates_extracted,  # Use pre-calculated value
                "action_items": email_analysis.get('action_items', safe_analysis_data.get('action_items', []))
            },

            "attachments": [],

            "additional_info": {
                "terms_conditions": additional_info.get('terms_conditions', ''),
                "return_policy": additional_info.get('return_policy', ''),
                "warranty": additional_info.get('warranty', ''),
                "notes": additional_info.get('notes', '')
            },

            "system_metadata": {
                "processed_at": datetime.now().isoformat(),
                "processing_version": "2.1",
                "confidence_score": 0.95,
                "extraction_method": "gemini_ai_multimodal"
            }
        }

        # STEP 1: PROCESS ITEMS WITH AMOUNT CALCULATIONS
        print(f"[WEBHOOK PREP] Processing items with amount calculations...")
        processed_items = []
        calculated_total = 0.0

        # Get items from the extracted items variable (which handles both nested and top-level items)
        items_list = items  # This was already extracted above
        print(f"[WEBHOOK PREP] Found {len(items_list)} items to process")
        print(f"[DEBUG] Raw items_list: {items_list}")

        # Also check if items are nested elsewhere in the analysis
        if not items_list and 'analysis' in safe_analysis_data:
            # Check attachment analysis for items
            for att in attachment_analysis:
                if isinstance(att, dict) and 'items' in att:
                    print(f"[DEBUG] Found items in attachment: {att['items']}")
                    items_list.extend(att['items'] if isinstance(att['items'], list) else [])
            
            # Check email analysis for items
            if isinstance(email_analysis, dict) and 'items' in email_analysis:
                print(f"[DEBUG] Found items in email_analysis: {email_analysis['items']}")
                items_list.extend(email_analysis['items'] if isinstance(email_analysis['items'], list) else [])

        print(f"[DEBUG] After checking all sources, items_list: {items_list}")

        for i, item in enumerate(items_list):
            try:
                print(f"[DEBUG] Processing item {i+1}: {item}")
                # Extract basic item data with field name variations
                quantity = float(item.get('quantity', item.get('qty', 0)))
                unit_price = float(item.get('unit_price', item.get('rate', item.get('price', 0))))
                line_total = item.get('total', item.get('line_total'))

                # AMOUNT CALCULATION LOGIC: Calculate line_total if missing or None
                calculated_line_total = 0.0
                if line_total is None or line_total == '' or (isinstance(line_total, str) and line_total.strip() == ''):
                    # Calculate from quantity × unit_price
                    calculated_line_total = quantity * unit_price
                    print(f"[WEBHOOK PREP] Item {i+1}: Calculated line_total = {quantity} × {unit_price} = {calculated_line_total}")
                else:
                    # Use existing total
                    try:
                        calculated_line_total = float(line_total)
                        print(f"[WEBHOOK PREP] Item {i+1}: Using existing line_total = {calculated_line_total}")
                    except (ValueError, TypeError):
                        # Fallback to calculation if conversion fails
                        calculated_line_total = quantity * unit_price
                        print(f"[WEBHOOK PREP] Item {i+1}: Fallback calculation line_total = {quantity} × {unit_price} = {calculated_line_total}")

                # Add to running total
                calculated_total += calculated_line_total

                # Extract item number from multiple possible fields
                item_number = (
                    item.get('item_number') or
                    item.get('sku') or
                    item.get('part_number') or
                    item.get('product_code') or
                    item.get('item_code') or
                    ''
                )

                # Create processed item with field name variations
                processed_item = {
                    "item_number": str(item_number).strip(),
                    "product_name": item.get('product_name', item.get('description', '')),
                    "description": item.get('description', ''),
                    "quantity": str(int(quantity)) if quantity == int(quantity) else str(quantity),
                    "unit_price": f"{unit_price:.2f}",
                    "line_total": f"{calculated_line_total:.2f}",
                    "unit_of_measure": item.get('unit_of_measure', item.get('unit', 'each')),
                    "category": item.get('category', ''),
                    "brand": item.get('brand', ''),
                    "model": item.get('model', ''),
                    "color": item.get('color', ''),
                    "size": item.get('size', ''),
                    "weight": item.get('weight', ''),
                    "dimensions": item.get('dimensions', ''),
                    "material": item.get('material', ''),
                    "compliance_info": item.get('compliance_info', ''),
                    "special_instructions": item.get('special_instructions', ''),
                    "discount_applied": item.get('discount_applied', ''),
                    "tax_applicable": item.get('tax_applicable', 'yes'),
                    "delivery_date": format_date_to_string(item.get('delivery_date')),
                    "warranty_info": item.get('warranty_info', ''),
                    "origin_country": item.get('origin_country', ''),
                    "customs_info": item.get('customs_info', '')
                }

                processed_items.append(processed_item)
                print(f"[WEBHOOK PREP] Item {i+1}: {processed_item['product_name']} - ${processed_item['line_total']}")

            except Exception as item_error:
                print(f"[WEBHOOK PREP] Error processing item {i+1}: {str(item_error)}")
                # Extract item number for error case too
                error_item_number = (
                    item.get('item_number') or
                    item.get('sku') or
                    item.get('part_number') or
                    item.get('product_code') or
                    item.get('item_code') or
                    ''
                )

                # Add a minimal item entry to avoid data loss
                processed_items.append({
                    "item_number": str(error_item_number).strip(),
                    "product_name": item.get('product_name', item.get('description', f'Item {i+1}')),
                    "description": item.get('description', ''),
                    "quantity": "1",
                    "unit_price": "0.00",
                    "line_total": "0.00",
                    "unit_of_measure": "each"
                })

        # Update payload with processed items
        payload["items"] = processed_items
        print(f"[WEBHOOK PREP] Processed {len(processed_items)} items, calculated subtotal: ${calculated_total:.2f}")

        # STEP 2: CALCULATE FINANCIAL DETAILS
        print(f"[WEBHOOK PREP] Processing financial details with amount calculations...")
        # financial_details was already extracted above

        # Get existing total_amount or use calculated total
        existing_total = financial_details.get('total_amount')
        final_total_amount = 0.0

        if existing_total is None or existing_total == '' or (isinstance(existing_total, str) and existing_total.strip() == ''):
            # Use calculated total from items
            final_total_amount = calculated_total
            print(f"[WEBHOOK PREP] Using calculated total_amount from items: ${final_total_amount:.2f}")
        else:
            # Use existing total
            try:
                final_total_amount = float(existing_total)
                print(f"[WEBHOOK PREP] Using existing total_amount: ${final_total_amount:.2f}")
            except (ValueError, TypeError):
                # Fallback to calculated total
                final_total_amount = calculated_total
                print(f"[WEBHOOK PREP] Fallback to calculated total_amount: ${final_total_amount:.2f}")

        # Build financial details
        payload["financial_details"] = {
            "currency": financial_details.get('currency', 'USD'),
            "subtotal": f"{calculated_total:.2f}",
            "tax_amount": f"{safe_float_conversion(financial_details.get('tax_amount'), 0.0):.2f}",
            "shipping_cost": f"{safe_float_conversion(financial_details.get('shipping_cost'), 0.0):.2f}",
            "discount_amount": f"{safe_float_conversion(financial_details.get('discount_amount'), 0.0):.2f}",
            "total_amount": f"{final_total_amount:.2f}",
            "tax_rate": financial_details.get('tax_rate', ''),
            "tax_type": financial_details.get('tax_type', ''),
            "payment_due_date": format_date_to_string(financial_details.get('payment_due_date')),
            "early_payment_discount": financial_details.get('early_payment_discount', ''),
            "late_payment_penalty": financial_details.get('late_payment_penalty', ''),
            "billing_period": financial_details.get('billing_period', ''),
            "cost_center": financial_details.get('cost_center', ''),
            "budget_code": financial_details.get('budget_code', ''),
            "po_limit": financial_details.get('po_limit', ''),
            "approved_by": financial_details.get('approved_by', ''),
            "approval_date": format_date_to_string(financial_details.get('approval_date'))
        }

        print(f"[WEBHOOK PREP] Financial details calculated - Total: ${payload['financial_details']['total_amount']}")

        # STEP 3: PROCESS ATTACHMENTS (existing code follows...)
        # Process attachments with blob data
        processed_attachments = []

        # Use attachment_analysis if available, otherwise use original attachments
        attachments_to_process = attachment_analysis if attachment_analysis else safe_analysis_data.get('attachments', [])

        for attachment in attachments_to_process:
            attachment_entry = {
                "filename": attachment.get('filename', ''),
                "content_type": attachment.get('content_type') or attachment.get('mimeType') or 'application/octet-stream',
                "size": attachment.get('size', 0),
                "url": attachment.get('url', ''),
                "viewer_url": "",
                "blob_data": "",
                "analysis": {
                    "document_type": attachment.get('type', 'Document'),
                    "extracted_data_confidence": 0.98,
                    "content_summary": attachment.get('content_summary', f"Attachment: {attachment.get('filename', 'unknown')}")
                }
            }

            # Download and convert to blob if URL is available
            if attachment.get('url'):
                # Add Google Document Viewer URL for PDFs and docs
                content_type = (attachment_entry["content_type"] or "").lower()
                if content_type in ['application/pdf', 'application/msword',
                                  'application/vnd.openxmlformats-officedocument.wordprocessingml.document']:
                    attachment_entry["viewer_url"] = f"https://docs.google.com/viewer?url={attachment['url']}&embedded=true"

                # Download and convert to blob
                attachment_data = download_attachment_from_url(attachment['url'])
                if attachment_data:
                    attachment_entry["blob_data"] = convert_attachment_to_blob(attachment_data)
                    attachment_entry["size"] = len(attachment_data)
                    print(f"[WEBHOOK PREP] Successfully processed attachment: {attachment.get('filename', 'unnamed')}")
                else:
                    print(f"[WEBHOOK PREP] Failed to download attachment: {attachment.get('filename', 'unnamed')}")

            processed_attachments.append(attachment_entry)

        payload["attachments"] = processed_attachments

        # Final logging
        print(f"[WEBHOOK PREP] Clean payload prepared successfully")
        print(f"[WEBHOOK PREP] Category: {payload['category']}")
        print(f"[WEBHOOK PREP] Document type: {payload['document_info']['type']}")
        print(f"[WEBHOOK PREP] Document number: {payload['document_info']['document_number']}")
        print(f"[WEBHOOK PREP] Items count: {len(payload['items'])}")
        print(f"[WEBHOOK PREP] Attachments count: {len(payload['attachments'])}")
        print(f"[WEBHOOK PREP] Total amount: {payload['financial_details']['total_amount']}")

        # IMPORTANT: Ensure no null/None values in the final payload
        # Convert all null/None values to empty strings
        clean_payload = ensure_no_null_values(payload)
        print(f"[WEBHOOK PREP] Applied null-to-empty-string conversion")

        return clean_payload

    except Exception as e:
        logger.error(f"Error preparing clean webhook payload: {str(e)}")
        logger.error(f"Traceback: {traceback.format_exc()}")
        print(f"[WEBHOOK ERROR] Failed to prepare clean payload: {str(e)}")
        # Return a minimal valid payload to avoid complete failure
        error_payload = {
            "company_id": "1006",
            "created_by": "1",
            "email_id": analysis_data.get('email_id', ''),
            "category": analysis_data.get('category', 'other'),
            "analyzed_at": datetime.now().strftime('%Y-%m-%d'),
            "error": f"Payload preparation failed: {str(e)}"
        }
        # Ensure no null values even in error case
        return ensure_no_null_values(error_payload)
    

def send_to_webhook(analysis_data: Dict[str, Any]) -> bool:
    """
    Send email analysis data to webhook using clean JSON structure
    """
    global webhook_call_tracker

    print(f"\n[WEBHOOK CALL] =========================")
    print(f"[WEBHOOK CALL] Function called with clean structure")
    print(f"[WEBHOOK CALL] =========================")

    try:
        # Create deduplication key
        email_id = analysis_data.get('email_id', '')
        email_fingerprint = analysis_data.get('email_fingerprint', '')
        webhook_key = f"{email_id}_{email_fingerprint}"

        # Enhanced duplicate checking with multiple keys
        current_time = time.time()

        # Create multiple deduplication keys for better tracking
        dedup_keys = [
            webhook_key,  # Original key
            f"{email_id}",  # Just email ID
            f"{email_fingerprint}" if email_fingerprint else None  # Just fingerprint
        ]

        # Remove None values
        dedup_keys = [key for key in dedup_keys if key]

        # Check if any of the keys indicate a recent webhook call
        for key in dedup_keys:
            if key in webhook_call_tracker:
                last_call_time = webhook_call_tracker[key]
                if current_time - last_call_time < 120:  # 2 minutes to be safe
                    print(f"[WEBHOOK SKIP] Skipping duplicate webhook call for key: {key}")
                    return True

        # Mark all keys as called
        for key in dedup_keys:
            webhook_call_tracker[key] = current_time

        # Clean up old tracker entries (keep for 10 minutes)
        cutoff_time = current_time - 600
        webhook_call_tracker = {k: v for k, v in webhook_call_tracker.items() if v > cutoff_time}

        # Check Firestore for webhook status to prevent duplicates across restarts
        try:
            from firebase_admin import firestore
            db = firestore.client()

            # Try to get user_id and account_id from analysis_data
            user_id = analysis_data.get('user_id', '')
            account_id = analysis_data.get('account_id', '')

            if user_id and account_id and email_id:
                composite_id = f"{account_id}_{email_id}"
                analysis_ref = db.collection('users').document(user_id).collection('email_analyses').document(composite_id)
                analysis_doc = analysis_ref.get()

                if analysis_doc.exists:
                    doc_data = analysis_doc.to_dict()
                    if doc_data.get('webhook_sent', False):
                        print(f"[WEBHOOK SKIP] Webhook already sent for {composite_id} according to Firestore")
                        return True

                    # Mark webhook as being sent
                    analysis_ref.update({
                        'webhook_sending': True,
                        'webhook_send_started_at': firestore.SERVER_TIMESTAMP
                    })
                    print(f"[WEBHOOK CALL] Marked webhook as being sent in Firestore for {composite_id}")
        except Exception as firestore_err:
            print(f"[WEBHOOK WARNING] Could not check Firestore webhook status: {str(firestore_err)}")

        # Prepare clean payload
        print(f"[WEBHOOK CALL] Preparing clean payload...")
        payload = prepare_webhook_payload(analysis_data)

        # Convert to JSON with additional safety checks
        try:
            json_payload = json.dumps(payload, indent=2, default=str)
        except Exception as json_error:
            logger.error(f"JSON serialization error: {str(json_error)}")
            # Try with a more aggressive conversion
            safe_payload = safe_convert_firestore_value(payload)
            json_payload = json.dumps(safe_payload, indent=2, default=str)

        print(f"\n[WEBHOOK JSON] =========================")
        print(f"[WEBHOOK JSON] CLEAN JSON PAYLOAD")
        print(f"[WEBHOOK JSON] =========================")
        print(json_payload)
        print(f"[WEBHOOK JSON] =========================")
        print(f"[WEBHOOK JSON] END CLEAN JSON PAYLOAD")
        print(f"[WEBHOOK JSON] =========================\n")

        # Send request
        headers = {'Content-Type': 'application/json'}
        print(f"[WEBHOOK SEND] Sending to: {WEBHOOK_URL}")

        response = requests.post(WEBHOOK_URL, data=json_payload, headers=headers, timeout=60)

        print(f"[WEBHOOK RESPONSE] Status Code: {response.status_code}")
        print(f"[WEBHOOK RESPONSE] Body: {response.text}")

        webhook_success = False
        if 200 <= response.status_code < 300:
            webhook_success = True
            print(f"[WEBHOOK SUCCESS] Clean payload sent successfully!")
            logger.info(f"WEBHOOK SUCCESS: Clean structure data sent to {WEBHOOK_URL} - Status {response.status_code}")

            try:
                response_json = response.json()
                print(f"[WEBHOOK SUCCESS] Response JSON: {response_json}")
                if response_json.get('status') == 'success':
                    print(f"[WEBHOOK SUCCESS] Data accepted - Quote ID: {response_json.get('quote_id', 'not provided')}")
            except:
                print(f"[WEBHOOK NOTE] Response was not valid JSON")
        else:
            print(f"[WEBHOOK ERROR] Failed to send clean payload - Status: {response.status_code}")
            logger.error(f"WEBHOOK ERROR: Failed to send clean data - Status: {response.status_code}, Response: {response.text}")

        # Update Firestore with webhook result
        try:
            if user_id and account_id and email_id:
                composite_id = f"{account_id}_{email_id}"
                analysis_ref = db.collection('users').document(user_id).collection('email_analyses').document(composite_id)

                update_data = {
                    'webhook_sent': webhook_success,
                    'webhook_sending': False,
                    'webhook_response_code': response.status_code,
                    'webhook_response_text': response.text[:500] if response.text else '',  # Limit response text
                }

                if webhook_success:
                    update_data['webhook_sent_at'] = firestore.SERVER_TIMESTAMP
                else:
                    update_data['webhook_failed_at'] = firestore.SERVER_TIMESTAMP

                analysis_ref.update(update_data)
                print(f"[WEBHOOK TRACKING] Updated Firestore with webhook result: {webhook_success}")
        except Exception as tracking_err:
            print(f"[WEBHOOK WARNING] Could not update Firestore webhook tracking: {str(tracking_err)}")

        return webhook_success

    except Exception as e:
        logger.error(f"WEBHOOK ERROR: Exception in clean webhook sender: {str(e)}")
        logger.error(f"WEBHOOK ERROR: Traceback: {traceback.format_exc()}")
        print(f"[WEBHOOK ERROR] Exception: {str(e)}")
        return False

# Keep existing utility functions
def clear_webhook_tracker():
    """Clear the webhook call tracker"""
    global webhook_call_tracker
    old_count = len(webhook_call_tracker)
    webhook_call_tracker.clear()
    print(f"[UTILITY] Cleared webhook tracker ({old_count} entries removed)")

def get_webhook_tracker_status():
    """Get current webhook tracker status"""
    print(f"[UTILITY] Webhook tracker status:")
    print(f"[UTILITY] Total tracked calls: {len(webhook_call_tracker)}")
    current_time = time.time()
    for key, timestamp in webhook_call_tracker.items():
        age = current_time - timestamp
        print(f"[UTILITY] {key}: {age:.1f} seconds ago")

def test_webhook_with_clean_structure():
    """Test webhook with clean structure"""
    sample_data = {
        "company_id": "1006",
        "created_by": "1",
        "email_id": "test_email_clean_123",
        "category": "purchase_order",
        "subject": "Test Clean PO Email",
        "from": "<EMAIL>",
        "to": "<EMAIL>",
        "date": "2025-05-28",
        "total_amount": "1000.00",
        "analysis": {
            "document_info": {
                "type": "Purchase Order",
                "po_number": "PO-CLEAN-001"
            },
            "financial_details": {
                "total_amount": "1000.00",
                "subtotal": "900.00",
                "total_tax": "100.00"
            },
            "items": [
                {
                    "item_number": "CLEAN-001",
                    "description": "Test Clean Item",
                    "qty": 1,
                    "rate": "900.00",
                    "total": "900.00"
                }
            ],
            "email_analysis": {
                "summary": "Clean test purchase order",
                "sentiment": "neutral"
            }
        },
        "attachments": []
    }

    print(f"[TEST] Testing clean webhook structure...")
    result = send_to_webhook(sample_data)
    print(f"[TEST] Clean webhook test result: {'SUCCESS' if result else 'FAILED'}")
    return result

if __name__ == "__main__":
    print("Testing clean webhook sender functionality...")
    test_webhook_with_clean_structure()