from fastapi import APIRouter, Depends, HTTPException, UploadFile, File, Form, status, BackgroundTasks
from pydantic import BaseModel, Field
from typing import Optional, List, Dict, Any, Union
import os
import json
import datetime
import base64
import tempfile
import time
import logging
import traceback
from email_parser import is_valid_attachment_id
import hashlib
import google.generativeai as genai
from firebase_admin import firestore, storage
from routers.auth_router import verify_token
import mimetypes
from PyPDF2 import PdfReader
import docx
import pandas as pd
import openpyxl
import csv
from PIL import Image
import requests
from .webhook_sender import send_to_webhook


# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger("gemini_router")

# Define the router
router = APIRouter()

# Rate limiting configuration
MAX_REQUESTS_PER_MINUTE = 30  # 30 requests per minute per user
REQUEST_WINDOW = 60  # 60 seconds window
MAX_TOKENS = 30000  # Gemini token limit (safe value)

# Track requests for rate limiting
user_request_tracker = {}

# Models with improved validation
class GeminiAnalysisRequest(BaseModel):
    email_id: str
    email_data: Dict[str, Any]
    attachment_texts: Optional[Dict[str, str]] = None
    account_id: str = Field(description="Account ID for the email")

class PdfExtractionRequest(BaseModel):
    email_id: str
    attachment_url: str
    filename: str
    account_id: str = Field(description="Account ID for the email")

class BatchAnalyzeRequest(BaseModel):
    query: str
    max_results: int = Field(default=10, ge=1, le=50, description="Maximum results to retrieve and analyze")
    account_id: str = Field(description="Account ID to analyze")

def check_rate_limit(user_id: str) -> bool:
    """
    Check if the user has exceeded the rate limit
    Returns True if request should proceed, False if rate limited
    """
    current_time = time.time()

    if user_id not in user_request_tracker:
        user_request_tracker[user_id] = []

    # Remove requests older than the window
    user_request_tracker[user_id] = [t for t in user_request_tracker[user_id]
                                    if current_time - t < REQUEST_WINDOW]

    # Check if user has exceeded the limit
    if len(user_request_tracker[user_id]) >= MAX_REQUESTS_PER_MINUTE:
        return False

    # Add current request timestamp
    user_request_tracker[user_id].append(current_time)
    return True

def create_analysis_fingerprint(email_data: Dict[str, Any]) -> str:
    """Create a fingerprint hash for an email to prevent duplicate analysis"""
    # Create a string from key email fields
    fingerprint_data = f"{email_data.get('subject', '')}{email_data.get('from', '')}{email_data.get('date', '')}"

    # Add a snippet of the body content if available
    if isinstance(email_data.get('body'), dict):
        # Try to use text content first, then HTML
        body_content = email_data['body'].get('text', email_data['body'].get('html', ''))
        fingerprint_data += body_content[:100]  # Just use first 100 chars for fingerprint
    elif isinstance(email_data.get('body'), str):
        fingerprint_data += email_data['body'][:100]

    # Create SHA-256 hash of the fingerprint data
    hasher = hashlib.sha256()
    hasher.update(fingerprint_data.encode('utf-8', errors='replace'))
    return hasher.hexdigest()

def extract_text_from_attachment(filepath):
    """Extract text content from various file types with improved error handling"""
    file_extension = os.path.splitext(filepath)[1].lower()

    try:
        if file_extension == '.pdf':
            # Extract text from PDF
            try:
                reader = PdfReader(filepath)
                text = ""
                for page in reader.pages:
                    text += page.extract_text() + "\n"
                return text
            except Exception as e:
                logger.error(f"Error extracting text from PDF: {str(e)}")
                return f"[Error extracting PDF text: {str(e)}]"

        elif file_extension == '.docx':
            # Extract text from DOCX
            try:
                doc = docx.Document(filepath)
                text = ""
                for para in doc.paragraphs:
                    text += para.text + "\n"
                return text
            except Exception as e:
                logger.error(f"Error extracting text from DOCX: {str(e)}")
                return f"[Error extracting DOCX text: {str(e)}]"

        elif file_extension in ['.xlsx', '.xls']:
            # Extract text from Excel
            try:
                df = pd.read_excel(filepath)
                return df.to_string()
            except Exception as e:
                logger.error(f"Error extracting text from Excel: {str(e)}")
                return f"[Error extracting Excel text: {str(e)}]"

        elif file_extension == '.csv':
            # Extract text from CSV
            try:
                with open(filepath, 'r', encoding='utf-8', errors='replace') as file:
                    reader = csv.reader(file)
                    text = ""
                    for row in reader:
                        text += ",".join(row) + "\n"
                    return text
            except Exception as e:
                logger.error(f"Error extracting text from CSV: {str(e)}")
                return f"[Error extracting CSV text: {str(e)}]"

        elif file_extension in ['.jpg', '.jpeg', '.png', '.gif']:
            # For images, just return a placeholder
            return "[IMAGE CONTENT]"

        else:
            # For other file types, try to read as text
            try:
                with open(filepath, 'r', encoding='utf-8', errors='replace') as file:
                    return file.read()
            except:
                return f"[UNSUPPORTED FILE TYPE: {file_extension}]"

    except Exception as e:
        logger.error(f"Error extracting text from file: {str(e)}")
        return f"[ERROR EXTRACTING TEXT: {str(e)}]"

def get_attachment_data_from_gmail(service, email_id, attachment):
    """Get attachment data directly from Gmail without storing in Firebase Storage"""
    try:
        # Check if we already have attachment data from Flanker
        if 'data' in attachment and attachment['data']:
            logger.info(f"Using pre-loaded attachment data for {attachment.get('filename', 'unknown')} from email {email_id}")
            return {
                'data': attachment['data'],
                'filename': attachment.get('filename', 'unknown'),
                'content_type': attachment.get('content_type', attachment.get('mimeType', 'application/octet-stream')),
                'size': len(attachment['data'])
            }

        # Get attachment ID
        attachment_id = attachment.get('id')
        if not attachment_id:
            logger.warning(f"Missing attachment ID for attachment {attachment.get('filename', 'unknown')} in email {email_id}")
            return None

        # Get the attachment data from Gmail API
        try:
            attachment_data = service.users().messages().attachments().get(
                userId='me',
                messageId=email_id,
                id=attachment_id
            ).execute()
        except Exception as e:
            logger.error(f"Error downloading attachment {attachment.get('filename', 'unknown')}: {str(e)}")
            return None

        # Check if data exists in response
        if 'data' not in attachment_data:
            logger.warning(f"No data found in attachment response for {attachment.get('filename', 'unknown')} in email {email_id}")
            return None

        # Decode the attachment data
        file_data = base64.urlsafe_b64decode(attachment_data['data'])

        # Determine content type based on file extension
        filename = attachment.get('filename', 'unknown')
        content_type = attachment.get('mimeType', attachment.get('content_type', 'application/octet-stream'))

        # Return the data and metadata
        return {
            'data': file_data,
            'filename': filename,
            'content_type': content_type,
            'size': len(file_data)
        }
    except Exception as e:
        logger.error(f"Error downloading attachment from Gmail: {str(e)}")
        raise

def send_pdf_to_gemini(api_key, filepath):
    """Send the entire PDF file directly to Gemini API for detailed extraction"""
    # Configure the Gemini API
    genai.configure(api_key=api_key)

    # Read the PDF file as binary data
    with open(filepath, 'rb') as f:
        pdf_bytes = f.read()

    # Encode the PDF in base64
    pdf_base64 = base64.b64encode(pdf_bytes).decode('utf-8')

    # Initialize Gemini model
    model = genai.GenerativeModel('gemini-2.5-flash')

    # Create a file part for the file
    file_part = {
        "inline_data": {
            "data": pdf_base64,
            "mime_type": "application/pdf"
        }
    }

    # Enhanced text prompt asking for more detailed extraction including subitems and all possible values
    text_prompt = """
    Extract ALL possible data from this PDF with extreme detail. I need:

    1. Document Identification:
       - Invoice/Order/Quote Number (exactly as shown)
       - Purchase Order Number (if different from invoice number)
       - Reference Numbers (any additional reference codes)
       - Document Type (invoice, order, quote, etc.)
       - Document Status (paid, pending, draft, etc.)

    2. Dates (extract ALL dates mentioned):
       - Invoice/Order Date
       - Due Date
       - Ship Date
       - Delivery Date
       - In-Hand Date
       - Payment Date
       - Any other dates mentioned with their context

    3. Parties Information (extract COMPLETE details - BE VERY CAREFUL ABOUT ROLES):
       
       IMPORTANT: Identify parties based on the DOCUMENT CONTENT, not the email sender:
       - For PURCHASE ORDERS: The issuer/sender of the PO is the CUSTOMER (buyer), the recipient is the VENDOR (seller)
       - For INVOICES: The issuer/sender of the invoice is the VENDOR (seller), the recipient is the CUSTOMER (buyer)
       - For QUOTES: The issuer is the VENDOR (seller), the recipient is the CUSTOMER (buyer)
       
       VENDOR IDENTIFICATION GUIDELINES:
       Companies with these characteristics are typically VENDORS (sellers/suppliers):
       - Uniform companies (e.g., "Spectrum Uniforms", "Cintas", "UniFirst", "Aramark")
       - Apparel/clothing manufacturers or suppliers
       - Promotional product companies
       - Manufacturing companies
       - Wholesale/distribution companies
       - Service providers
       - Companies with names ending in "Uniforms", "Apparel", "Manufacturing", "Supply", "Services"
       - Companies that appear to be providing/selling products or services
       
       CUSTOMER IDENTIFICATION GUIDELINES:
       Companies that are typically CUSTOMERS (buyers/purchasers):
       - Schools, universities, educational institutions
       - Hospitals, healthcare facilities
       - Restaurants, hotels, hospitality businesses
       - Corporate offices, businesses purchasing uniforms/services
       - Government agencies
       - Non-profit organizations
       
       Extract these specific roles:
       - Vendor/Supplier/Seller (the company PROVIDING goods/services - full name, address, phone, email, tax ID, website)
       - Customer/Buyer/Purchaser (the company PURCHASING goods/services - full name, address, phone, email, customer ID)
       - Ship-to Address (if different from billing)
       - Bill-to Address (if specified separately)
       - Contact Persons (names, titles, contact info)
       - Sales Representative information
       
       ADDRESS PARSING INSTRUCTIONS:
       When extracting addresses, parse them into structured components:
       - address_line1: Primary street address (street number, name, direction)
       - address_line2: Secondary address info (apartment, suite, unit, floor, building)
       - city: City name only
       - state: State, province, or region (use standard abbreviations when possible)
       - country: Country name (use full country name or standard abbreviation)
       - zip_code: Postal code, ZIP code, or equivalent
       
       Examples of proper address parsing:
       "123 Main St, Suite 456, New York, NY 10001" should become:
       - address_line1: "123 Main St"
       - address_line2: "Suite 456"
       - city: "New York"
       - state: "NY"
       - country: "USA" (if not specified, infer from context)
       - zip_code: "10001"

    4. Order/Delivery Details:
       - Delivery Method
       - Shipping Carrier
       - Tracking Number
       - Shipping Terms
       - Delivery Instructions
       - Estimated Arrival
       - FOB Terms
       - Incoterms
       - Order Status

    5. Payment Information:
       - Payment Terms
       - Payment Method
       - Payment Instructions
       - Account Information
       - Currency
       - Exchange Rates (if applicable)
       - Early Payment Discounts
       - Late Payment Penalties

    6. Line Items (extract EVERY detail for EACH item - EXTRACT ALL POSSIBLE ITEM ATTRIBUTES AND SPECIFICATIONS):
       - Primary Items:
         * Item Number/SKU/Part Number
         * Quantity (with units)
         * Description (full text exactly as shown)
         * Unit Price
         * Discount (if any)
         * Tax Rate (if specified per item)
         * Line Total
         * **CRITICAL: Extract ALL Product Attributes and Specifications (search the ENTIRE document)**
           - COLOR: Look for any color specification (Black, White, Red, Navy, etc.)
           - SIZE: Look for any size information (S, M, L, XL, XXL, 32, 34, Small, Medium, Large, etc.)
           - MATERIAL: Fabric type, composition (Cotton, Polyester, Blend, etc.)
           - STYLE: Product style codes or descriptions
           - BRAND: Manufacturer or brand name
           - MODEL: Product model numbers
           - FINISH: Surface finish or texture
           - WEIGHT: Product weight or fabric weight (GSM, oz, etc.)
           - DIMENSIONS: Length, width, height measurements
           - FABRIC: Fabric type, weave, texture
           - PATTERN: Stripes, solids, prints, etc.
           - FIT: Regular, slim, relaxed, etc.
           - GENDER: Men's, Women's, Unisex
           - AGE_GROUP: Adult, Youth, Child, etc.
           - SLEEVE_TYPE: Short sleeve, long sleeve, sleeveless
           - COLLAR_TYPE: Polo, crew neck, V-neck, etc.
           - CLOSURE: Button, zipper, snap, etc.
           - POCKETS: Number and type of pockets
           - PERFORMANCE: Moisture-wicking, UV protection, antimicrobial, etc.
           - CARE_INSTRUCTIONS: Washing, drying instructions
           - CERTIFICATIONS: Flame retardant, safety standards, etc.
           - ORIGIN: Made in, country of origin
           - SEASONAL: Summer, winter, all-season
           - SPECIAL_FEATURES: Reflective strips, reinforced areas, etc.

       **COMPREHENSIVE ATTRIBUTE EXTRACTION GUIDELINES:**
       - Search the ENTIRE document for item specifications, not just the item description line
       - Look for separate columns labeled "Color", "Size", "Style", "Material", "Specifications", etc.
       - Check product specification sheets or detailed descriptions
       - Examine any technical drawings or diagrams
       - Look for variant tables or option lists
       - Check for abbreviations and expand them: BLK=Black, WHT=White, NVY=Navy, CTN=Cotton, POLY=Polyester
       - Look for size charts, material compositions, and care labels
       - Check for variant SKUs that may include encoded specifications
       - Include any specifications mentioned in item descriptions, notes, or comments
       - If multiple variants exist, list each as a separate line item with full specifications
       - Extract decoration/customization details separately
       - Look for performance specifications (breathability, durability ratings, etc.)
       - Check for compliance and safety certifications
       - Include any special ordering instructions or customization notes

       - Subitems/Components:
         * Component Description
         * Component Quantity
         * Component Price (if listed)
         * Component Specifications
         * Component Material and Attributes

       - Customization/Decoration Details:
         * Decoration Type (print, embroidery, screen print, heat transfer, etc.)
         * Decoration Location (left chest, back, sleeve, etc.)
         * Decoration Colors
         * Decoration Size/Dimensions
         * Artwork References or PMS colors
         * Setup Charges
         * Thread colors for embroidery
         * Print methods and inks

    7. Financial Details (extract ALL amounts):
       - Item Subtotal
       - Discounts (with descriptions)
       - Surcharges (with descriptions)
       - Tax Breakdown (by type and rate)
       - Shipping/Freight Costs
       - Handling Fees
       - Insurance
       - Total Tax Amount
       - Total Amount
       - Amount Paid
       - Balance Due
       - Deposit Amount
       - Tip/Gratuity (if applicable)

    8. Additional Information:
       - Terms and Conditions
       - Return Policy
       - Warranty Information
       - Notes/Comments
       - Special Instructions
       - Disclaimers
       - Customer Service Information
       - Promotional Messages
       - Signature Requirements

    Format your response as a comprehensive JSON object that includes ALL extracted information. Use nested objects and arrays as appropriate. If you cannot extract certain information, use null for that field, but try to extract as much as possible.

    {
      "document_info": {
        "type": "document type (invoice, order, quote, etc.)",
        "invoice_number": "extracted invoice number",
        "order_number": "extracted order number",
        "po_number": "purchase order number",
        "reference_numbers": ["ref1", "ref2"],
        "status": "document status"
      },
      "dates": {
        "invoice_date": "invoice date",
        "due_date": "payment due date",
        "order_date": "order date",
        "ship_date": "ship date",
        "delivery_date": "delivery date",
        "in_hand_date": "in-hand date",
        "other_dates": [
          {"date": "other date", "description": "context of this date"}
        ]
      },
      "parties": {
        "vendor": {
          "name": "vendor name",
          "address": {
            "address_line1": "street address line 1",
            "address_line2": "street address line 2 (apt, suite, etc.)",
            "city": "city name",
            "state": "state/province",
            "country": "country name",
            "zip_code": "postal/zip code"
          },
          "phone": "phone number",
          "email": "email address",
          "website": "website",
          "tax_id": "tax ID"
        },
        "customer": {
          "name": "customer name",
          "address": {
            "address_line1": "street address line 1",
            "address_line2": "street address line 2 (apt, suite, etc.)",
            "city": "city name",
            "state": "state/province",
            "country": "country name",
            "zip_code": "postal/zip code"
          },
          "phone": "phone number",
          "email": "email address",
          "customer_id": "customer ID"
        },
        "ship_to": {
          "name": "recipient name",
          "address": {
            "address_line1": "street address line 1",
            "address_line2": "street address line 2 (apt, suite, etc.)",
            "city": "city name",
            "state": "state/province",
            "country": "country name",
            "zip_code": "postal/zip code"
          },
          "phone": "phone number"
        },
        "bill_to": {
          "name": "billing name",
          "address": {
            "address_line1": "street address line 1",
            "address_line2": "street address line 2 (apt, suite, etc.)",
            "city": "city name",
            "state": "state/province",
            "country": "country name",
            "zip_code": "postal/zip code"
          }
        },
        "contacts": [
          {"name": "contact name", "title": "position", "phone": "contact phone", "email": "contact email"}
        ],
        "sales_rep": {
          "name": "sales rep name",
          "id": "sales rep ID",
          "contact": "sales rep contact info"
        }
      },
      "order_details": {
        "delivery_method": "shipping method",
        "carrier": "shipping carrier",
        "tracking_number": "tracking number",
        "shipping_terms": "shipping terms",
        "delivery_instructions": "special instructions",
        "estimated_arrival": "ETA",
        "fob": "FOB information",
        "incoterms": "international shipping terms",
        "order_status": "status of order"
      },
      "payment_info": {
        "terms": "payment terms",
        "method": "payment method",
        "instructions": "payment instructions",
        "account_info": "account information",
        "currency": "currency code",
        "exchange_rate": "exchange rate if applicable",
        "early_payment_discount": "early payment terms",
        "late_payment_penalty": "late payment terms"
      },
      "items": [
        {
          "item_number": "SKU/part number",
          "qty": "quantity",
          "unit": "unit of measure",
          "description": "primary item description",
          "color": "EXACT color as specified (Black, White, Navy, Red, etc.)",
          "size": "EXACT size as specified (S, M, L, XL, 32, 34, Small, Medium, Large, etc.)",
          "material": "material type (Cotton, Polyester, Blend, etc.)",
          "brand": "brand or manufacturer name",
          "model": "model number or style code",
          "fabric": "fabric type, weave, texture details",
          "pattern": "stripes, solids, prints, etc.",
          "fit": "regular, slim, relaxed, etc.",
          "gender": "Men's, Women's, Unisex",
          "age_group": "Adult, Youth, Child, etc.",
          "sleeve_type": "short sleeve, long sleeve, sleeveless",
          "collar_type": "polo, crew neck, V-neck, etc.",
          "closure": "button, zipper, snap, etc.",
          "pockets": "number and type of pockets",
          "performance": "moisture-wicking, UV protection, antimicrobial, etc.",
          "care_instructions": "washing, drying instructions",
          "certifications": "flame retardant, safety standards, etc.",
          "origin": "made in, country of origin",
          "seasonal": "summer, winter, all-season",
          "special_features": "reflective strips, reinforced areas, etc.",
          "attributes": {
            "finish": "surface finish or texture",
            "weight": "product weight or fabric weight (GSM, oz, etc.)",
            "dimensions": "length x width x height",
            "style": "style description or code",
            "other_specifications": "any other product specifications not covered above"
          },
          "rate": "unit price",
          "discount": "item discount",
          "tax_rate": "tax rate for this item",
          "total": "line total - calculate as quantity × rate if not shown",
          "subitems": [
            {
              "description": "subitem description",
              "qty": "subitem quantity",
              "rate": "subitem unit price",
              "total": "subitem total",
              "color": "subitem color if different",
              "size": "subitem size if different",
              "material": "subitem material if different",
              "specifications": "subitem specifications"
            }
          ],
          "decoration": {
            "type": "decoration type (print, embroidery, screen print, heat transfer, etc.)",
            "location": "decoration location (left chest, back, sleeve, etc.)",
            "colors": "decoration colors",
            "size_dimensions": "decoration size/dimensions",
            "artwork": "artwork reference or PMS colors",
            "setup_charge": "setup fee",
            "thread_colors": "thread colors for embroidery",
            "print_method": "print methods and inks"
          }
        }
      ],
      "financial_details": {
        "subtotal": "items subtotal",
        "discounts": [
          {"description": "discount description", "amount": "discount amount"}
        ],
        "surcharges": [
          {"description": "surcharge description", "amount": "surcharge amount"}
        ],
        "tax_breakdown": [
          {"type": "tax type", "rate": "tax rate", "amount": "tax amount"}
        ],
        "shipping": "shipping cost",
        "handling": "handling fees",
        "insurance": "insurance cost",
        "total_tax": "total tax amount",
        "total_amount": "total amount - calculate from quantity × unit_price if not directly stated",
        "amount_paid": "amount already paid",
        "balance_due": "remaining balance",
        "deposit": "deposit amount",
        "gratuity": "tip amount"
      },
      "additional_info": {
        "terms_conditions": "terms and conditions",
        "return_policy": "return policy",
        "warranty": "warranty information",
        "notes": "notes or comments",
        "special_instructions": "special instructions",
        "disclaimers": "disclaimers",
        "customer_service": "customer service info",
        "promotional_message": "promotional content",
        "signature_required": true/false
      }
    }

    IMPORTANT: Only return valid JSON. Do not include any markdown formatting, explanations, or notes before or after the JSON. Just return the JSON object and nothing else. If you cannot extract certain information, use null for that field, but try to extract EVERYTHING possible from the document.
    """

    try:
        # Generate content using the PDF and the prompt
        response = model.generate_content([text_prompt, file_part])

        # Get the response text
        response_text = response.text

        # Clean up the response to ensure it's valid JSON
        if '```json' in response_text and '```' in response_text:
            # Extract JSON from code block
            json_content = response_text.split('```json')[1].split('```')[0].strip()
            extraction_json = json.loads(json_content)
        else:
            # Try to parse the entire string as JSON
            response_text = response_text.strip()

            # Try to extract only the valid JSON part
            import re
            json_pattern = r'[\s\S]*?(\{[\s\S]*\})[\s\S]*'
            match = re.match(json_pattern, response_text)
            if match:
                # Extract just the JSON object part
                json_text = match.group(1)

                # Further cleanup - try to handle incomplete or malformed JSON
                # Remove any trailing commas before closing brackets (common JSON error)
                json_text = re.sub(r',\s*}', '}', json_text)
                json_text = re.sub(r',\s*]', ']', json_text)

                extraction_json = json.loads(json_text)
            else:
                extraction_json = json.loads(response_text)

        # Add a field to indicate the extraction method
        extraction_json['extraction_method'] = 'gemini_direct_pdf'

        # Add timestamp
        extraction_json['extracted_at'] = datetime.datetime.now().isoformat()

        return extraction_json

    except Exception as e:
        # If there's an exception, return error information
        logger.error(f"Error processing PDF: {str(e)}")
        return {
            "error": True,
            "message": f"Error processing PDF: {str(e)}",
            "extraction_method": "gemini_direct_pdf_failed"
        }


# Updated functions for gemini_router.py

def analyze_with_gemini_direct(api_key, email_data, attachment_texts=None, attachment_contents=None):
    """Analyze the email and its direct attachment contents using Gemini with clean JSON structure"""
    # Configure the Gemini API
    genai.configure(api_key=api_key)

    # Initialize Gemini model with increased timeout
    model = genai.GenerativeModel('gemini-2.5-flash')

    # Handle different body formats
    body_text = ""
    if isinstance(email_data.get('body'), dict):
        body_text = email_data['body'].get('text', email_data['body'].get('html', ''))
    elif isinstance(email_data.get('body'), str):
        body_text = email_data['body']

    # Limit body text to avoid token limits
    if len(body_text) > 10000:  # Roughly 2500 tokens
        logger.info(f"Truncating email body from {len(body_text)} to 10000 characters")
        body_text = body_text[:10000] + "... [content truncated]"

    # Prepare the email content for analysis
    email_content = f"""
    From: {email_data.get('from', 'Unknown')}
    Subject: {email_data.get('subject', 'No Subject')}
    Date: {email_data.get('date', 'Unknown Date')}

    Body:
    {body_text}
    """

    # Prepare multimodal content
    multimodal_content = []
    has_attachments = False

    # Updated prompt for clean JSON structure
    prompt_text = """
    You are an AI assistant specialized in analyzing business emails and their attachments. Extract ALL detailed information and return it in this EXACT clean JSON structure:

    {
      "category": "purchase_order|invoice|order_confirmation|inquiry|notification|marketing|personal|other",
      
      "document_info": {
        "type": "Purchase Order|Invoice|Order Confirmation|Quote|Receipt|Other",
        "document_number": "main document identifier",
        "po_number": "purchase order number if found",
        "invoice_number": "invoice number if found", 
        "reference_numbers": ["any additional reference codes"],
        "status": "pending|approved|paid|shipped|delivered|cancelled"
      },
      
      "email_metadata": {
        "subject": "email subject",
        "from": "sender email/name",
        "to": "recipient email/name", 
        "cc": "cc recipients",
        "timestamp": "email date in YYYY-MM-DD format",
        "has_attachments": true/false,
        "attachment_count": number_of_attachments,
        "attachment_names": ["list of attachment filenames"]
      },
      
      "parties": {
        "vendor": {
          "name": "vendor/supplier name",
          "address": {
            "address_line1": "street address line 1",
            "address_line2": "street address line 2 (apt, suite, etc.)",
            "city": "city name",
            "state": "state/province",
            "country": "country name",
            "zip_code": "postal/zip code"
          },
          "phone": "vendor phone",
          "email": "vendor email",
          "tax_id": "vendor tax ID"
        },
        "customer": {
          "name": "customer/buyer name",
          "address": {
            "address_line1": "street address line 1",
            "address_line2": "street address line 2 (apt, suite, etc.)",
            "city": "city name",
            "state": "state/province",
            "country": "country name",
            "zip_code": "postal/zip code"
          },
          "phone": "customer phone", 
          "email": "customer email",
          "customer_id": "customer identifier"
        },
        "ship_to": {
          "name": "shipping recipient name",
          "address": {
            "address_line1": "street address line 1",
            "address_line2": "street address line 2 (apt, suite, etc.)",
            "city": "city name",
            "state": "state/province",
            "country": "country name",
            "zip_code": "postal/zip code"
          },
          "phone": "shipping contact phone"
        }
      },
      
      "dates": {
        "document_date": "document/order/invoice date in YYYY-MM-DD",
        "due_date": "payment due date in YYYY-MM-DD",
        "ship_date": "shipping date in YYYY-MM-DD", 
        "delivery_date": "delivery date in YYYY-MM-DD",
        "payment_date": "payment date in YYYY-MM-DD"
      },
      
      "financial_details": {
        "currency": "USD|EUR|GBP|etc",
        "subtotal": "items subtotal amount",
        "tax_total": "total tax amount",
        "shipping_cost": "shipping/freight cost",
        "total_amount": "final total amount - calculate from quantity × unit_price if not directly stated",
        "amount_paid": "amount already paid", 
        "balance_due": "remaining balance",
        "payment_terms": "payment terms (Net 30, etc)"
      },
      
      "items": [
        {
          "item_number": "SKU/part number",
          "qty": "quantity",
          "unit": "unit of measure",
          "description": "primary item description",
          "color": "EXACT color as specified (Black, White, Navy, Red, etc.)",
          "size": "EXACT size as specified (S, M, L, XL, 32, 34, Small, Medium, Large, etc.)",
          "material": "material type (Cotton, Polyester, Blend, etc.)",
          "brand": "brand or manufacturer name",
          "model": "model number or style code",
          "fabric": "fabric type, weave, texture details",
          "pattern": "stripes, solids, prints, etc.",
          "fit": "regular, slim, relaxed, etc.",
          "gender": "Men's, Women's, Unisex",
          "age_group": "Adult, Youth, Child, etc.",
          "sleeve_type": "short sleeve, long sleeve, sleeveless",
          "collar_type": "polo, crew neck, V-neck, etc.",
          "closure": "button, zipper, snap, etc.",
          "pockets": "number and type of pockets",
          "performance": "moisture-wicking, UV protection, antimicrobial, etc.",
          "care_instructions": "washing, drying instructions",
          "certifications": "flame retardant, safety standards, etc.",
          "origin": "made in, country of origin",
          "seasonal": "summer, winter, all-season",
          "special_features": "reflective strips, reinforced areas, etc.",
          "attributes": {
            "finish": "surface finish or texture",
            "weight": "product weight or fabric weight (GSM, oz, etc.)",
            "dimensions": "length x width x height",
            "style": "style description or code",
            "other_specifications": "any other product specifications not covered above"
          },
          "rate": "unit price",
          "discount": "item discount",
          "tax_rate": "tax rate for this item",
          "total": "line total - calculate as quantity × rate if not shown",
          "subitems": [
            {
              "description": "subitem description",
              "qty": "subitem quantity",
              "rate": "subitem unit price",
              "total": "subitem total",
              "color": "subitem color if different",
              "size": "subitem size if different",
              "material": "subitem material if different",
              "specifications": "subitem specifications"
            }
          ],
          "decoration": {
            "type": "decoration type (print, embroidery, screen print, heat transfer, etc.)",
            "location": "decoration location (left chest, back, sleeve, etc.)",
            "colors": "decoration colors",
            "size_dimensions": "decoration size/dimensions",
            "artwork": "artwork reference or PMS colors",
            "setup_charge": "setup fee",
            "thread_colors": "thread colors for embroidery",
            "print_method": "print methods and inks"
          }
        }
      ],
      
      "order_details": {
        "delivery_method": "shipping method",
        "carrier": "shipping carrier",
        "tracking_number": "tracking number",
        "shipping_terms": "FOB terms, etc",
        "estimated_delivery": "estimated delivery in YYYY-MM-DD",
        "special_instructions": "delivery instructions"
      },
      
      "payment_info": {
        "payment_method": "Check|Credit Card|Wire|etc",
        "payment_terms": "payment terms",
        "early_payment_discount": "early pay discount terms",
        "late_payment_penalty": "late payment terms"
      },
      
      "analysis_results": {
        "summary": "comprehensive summary of the email and attachments",
        "sentiment": "positive|negative|neutral",
        "confidence_score": 0.0-1.0,
        "key_dates_extracted": number_of_dates_found,
        "action_items": ["list of action items or next steps"]
      },
      
      "attachments": [
        {
          "filename": "attachment filename",
          "content_type": "MIME type",
          "size": file_size_in_bytes,
          "analysis": {
            "document_type": "type of document in attachment",
            "extracted_data_confidence": 0.0-1.0,
            "content_summary": "summary of attachment content"
          }
        }
      ],
      
      "additional_info": {
        "terms_conditions": "terms and conditions text",
        "return_policy": "return policy information", 
        "warranty": "warranty information",
        "notes": "additional notes or comments"
      }
    }

    CRITICAL REQUIREMENTS:
    1. Return ONLY valid JSON in this exact structure
    2. Use YYYY-MM-DD format for ALL dates
    3. Extract EVERY possible detail from email and attachments
    4. Do not include any markdown formatting or explanations
    5. Use null for missing fields, never omit required structure
    6. Ensure all financial amounts are strings
    7. Category must be one of the specified values
    
    AMOUNT CALCULATION REQUIREMENTS:
    - If total_amount is not directly visible, calculate it from line items (sum of quantity × unit_price)
    - If line_total is not shown for items, calculate as quantity × unit_price
    - Always show numerical calculations clearly
    - For multiple items, sum all line totals to get total_amount
    - Include tax and shipping in final total_amount calculation
    
    COLOR AND SIZE EXTRACTION REQUIREMENTS:
    - COLOR: Look for any color specification in item descriptions, separate color columns, or SKU codes
    - SIZE: Look for size information in item descriptions, size columns, or product specifications
    - Check for common abbreviations: BLK=Black, WHT=White, NVY=Navy, SM=Small, MED=Medium, LG=Large
    - Search the entire document, not just the item description line
    - If multiple colors/sizes are ordered for the same item, treat each as a separate line item
    - Extract exact color and size as written in the document
    - Look for size charts or specification tables
    - Check variant SKUs that may encode size/color information
    
    COMPREHENSIVE ITEM ATTRIBUTE EXTRACTION REQUIREMENTS:
    - Extract ALL possible item specifications and attributes from the ENTIRE document
    - Look for separate specification columns: Color, Size, Material, Style, Brand, Model, etc.
    - Check product specification sheets, technical drawings, and detailed descriptions
    - Include fabric details: type, weight (GSM, oz), weave, texture, composition
    - Extract fit and style information: regular, slim, relaxed, etc.
    - Capture demographic details: Men's, Women's, Unisex, Adult, Youth, Child
    - Look for garment specifics: sleeve type, collar type, closure type, pocket details
    - Extract performance features: moisture-wicking, UV protection, antimicrobial, breathability
    - Include care instructions: washing, drying, ironing requirements
    - Capture certifications: flame retardant, safety standards, compliance info
    - Look for origin information: Made in, Country of origin
    - Extract seasonal information: Summer, Winter, All-season
    - Include special features: reflective strips, reinforced areas, ventilation
    - Handle abbreviations: CTN=Cotton, POLY=Polyester, BLK=Black, etc.
    - Check variant tables and option lists for comprehensive specifications
    - Extract decoration details separately with full specifications
    - Look for performance ratings and durability specifications
    - Include any special ordering instructions or customization notes
    
    IMPORTANT PARTY IDENTIFICATION GUIDANCE:
    - Focus on the DOCUMENT CONTENT, not the email sender information
    - For PURCHASE ORDERS: The issuer/sender of the PO is the CUSTOMER (buyer), the recipient is the VENDOR (seller)
    - For INVOICES: The issuer/sender of the invoice is the VENDOR (seller), the recipient is the CUSTOMER (buyer)
    - For QUOTES: The issuer is the VENDOR (seller), the recipient is the CUSTOMER (buyer)
    
    VENDOR IDENTIFICATION GUIDELINES:
    Companies with these characteristics are typically VENDORS (sellers/suppliers):
    - Uniform companies (e.g., "Spectrum Uniforms", "Cintas", "UniFirst", "Aramark")
    - Apparel/clothing manufacturers or suppliers
    - Promotional product companies
    - Manufacturing companies
    - Wholesale/distribution companies
    - Service providers
    - Companies with names ending in "Uniforms", "Apparel", "Manufacturing", "Supply", "Services"
    - Companies that appear to be providing/selling products or services
    
    CRITICAL VENDOR IDENTIFICATION RULES:
    - ANY company with "Spectrum" in the name (especially "Spectrum Uniforms") should ALWAYS be identified as VENDOR
    - Spectrum is a major uniform supplier/manufacturer and should never be classified as customer
    
    CUSTOMER IDENTIFICATION GUIDELINES:
    Companies that are typically CUSTOMERS (buyers/purchasers):
    - Schools, universities, educational institutions
    - Hospitals, healthcare facilities
    - Restaurants, hotels, hospitality businesses
    - Corporate offices, businesses purchasing uniforms/services
    - Government agencies
    - Non-profit organizations
    
    - VENDOR/SUPPLIER: The business that provides goods/services (sells to customer)
    - CUSTOMER/BUYER: The business that purchases goods/services (buys from vendor)
    - Extract parties based on their business relationship roles, not email metadata
    
    ADDRESS PARSING INSTRUCTIONS:
    When extracting addresses, parse them into structured components:
    - address_line1: Primary street address (street number, name, direction)
    - address_line2: Secondary address info (apartment, suite, unit, floor, building)
    - city: City name only
    - state: State, province, or region (use standard abbreviations when possible)
    - country: Country name (use full country name or standard abbreviation)
    - zip_code: Postal code, ZIP code, or equivalent
    
    Examples of proper address parsing:
    "123 Main St, Suite 456, New York, NY 10001" should become:
    - address_line1: "123 Main St"
    - address_line2: "Suite 456"
    - city: "New York"
    - state: "NY"
    - country: "USA" (if not specified, infer from context)
    - zip_code: "10001"
    
    Analyze the following email and attachments:
    """

    # Add the prompt as the first text element
    multimodal_content.append(prompt_text)

    # Add the email content as the second element
    multimodal_content.append(email_content)

    # Add attachment text if available
    if attachment_texts and len(attachment_texts) > 0:
        attachment_text = "\n\nAttachment Text Content:\n"
        for filename, text in attachment_texts.items():
            attachment_text += f"\n--- {filename} ---\n"
            # Limit text length to avoid token limits
            if len(text) > 3000:
                logger.info(f"Truncating attachment text for {filename} from {len(text)} to 3000 characters")
                attachment_text += text[:3000] + "... [content truncated]"
            else:
                attachment_text += text
        multimodal_content.append(attachment_text)
        has_attachments = True

    # Process direct attachment contents (same as before, but update structure)
    if attachment_contents and len(attachment_contents) > 0:
        logger.info(f"Processing {len(attachment_contents)} direct attachments for Gemini analysis")

        for attachment in attachment_contents:
            try:
                content_type = attachment['content_type'].lower()
                filename = attachment['filename']
                file_data = attachment['data']

                # Process based on content type (same logic as before)
                if 'application/pdf' in content_type or filename.lower().endswith('.pdf'):
                    logger.info("Processing PDF attachment directly")
                    pdf_base64 = base64.b64encode(file_data).decode('utf-8')
                    pdf_part = {
                        "inline_data": {
                            "data": pdf_base64,
                            "mime_type": "application/pdf"
                        }
                    }
                    multimodal_content.append(pdf_part)
                    has_attachments = True

                elif 'image/' in content_type or any(filename.lower().endswith(ext) for ext in ['.jpg', '.jpeg', '.png', '.gif']):
                    logger.info("Processing image attachment directly")
                    img_base64 = base64.b64encode(file_data).decode('utf-8')
                    image_part = {
                        "inline_data": {
                            "data": img_base64,
                            "mime_type": content_type
                        }
                    }
                    multimodal_content.append(image_part)
                    has_attachments = True

                elif filename.lower().endswith('.eml'):
                    logger.info("Processing .eml attachment")
                    try:
                        from email import message_from_bytes
                        from email.policy import default

                        msg = message_from_bytes(file_data, policy=default)
                        eml_from = msg.get('From', 'Unknown')
                        eml_to = msg.get('To', 'Unknown')
                        eml_subject = msg.get('Subject', 'No Subject')
                        eml_date = msg.get('Date', 'Unknown Date')

                        eml_body = ""
                        if msg.is_multipart():
                            for part in msg.walk():
                                content_type = part.get_content_type()
                                if content_type == 'text/plain' or content_type == 'text/html':
                                    try:
                                        eml_body += part.get_content()
                                    except:
                                        eml_body += "[Content could not be decoded]"
                        else:
                            try:
                                eml_body = msg.get_content()
                            except:
                                eml_body = "[Content could not be decoded]"

                        if len(eml_body) > 5000:
                            logger.info(f"Truncating EML body from {len(eml_body)} to 5000 characters")
                            eml_body = eml_body[:5000] + "... [content truncated]"

                        eml_content = f"\n\n--- EMBEDDED EMAIL ATTACHMENT ---\nFrom: {eml_from}\nTo: {eml_to}\nSubject: {eml_subject}\nDate: {eml_date}\n\nBody:\n{eml_body}\n--- END OF EMBEDDED EMAIL ---\n"
                        multimodal_content.append(eml_content)
                        has_attachments = True

                    except Exception as e:
                        logger.error(f"Error processing .eml attachment: {str(e)}")
                        eml_note = f"\n\nEmail Attachment: {filename}\nError: {str(e)}\n"
                        multimodal_content.append(eml_note)
                        has_attachments = True

                elif content_type == 'text/plain' or any(filename.lower().endswith(ext) for ext in ['.txt', '.csv']):
                    logger.info(f"Processing text document: {filename}")
                    try:
                        text_content = file_data.decode('utf-8', errors='replace')
                        if len(text_content) > 3000:
                            logger.info(f"Truncating text document from {len(text_content)} to 3000 characters")
                            text_preview = text_content[:3000] + "... [content truncated]"
                        else:
                            text_preview = text_content

                        doc_content = f"\n\nText Document Content from {filename}:\n{text_preview}\n"
                        multimodal_content.append(doc_content)
                        has_attachments = True
                    except Exception as e:
                        logger.error(f"Error decoding text document: {str(e)}")
                        doc_note = f"\n\nDocument: {filename}\nError: {str(e)}\n"
                        multimodal_content.append(doc_note)
                        has_attachments = True

                else:
                    logger.info(f"Processing generic attachment with content type: {content_type}")
                    attachment_note = f"\n\nAttachment: {filename}\nContent-Type: {content_type}\nSize: {len(file_data)} bytes\n"
                    multimodal_content.append(attachment_note)
                    has_attachments = True

            except Exception as e:
                logger.error(f"Error processing attachment {attachment['filename']}: {str(e)}")
                attachment_note = f"\n\nError processing attachment {attachment['filename']}: {str(e)}\n"
                multimodal_content.append(attachment_note)

    # Add a note about attachments if they exist
    if has_attachments:
        multimodal_content.append("\n\nNote: This email contains attachments that have been analyzed above.")

    try:
        logger.info(f"Sending content to Gemini with {len(multimodal_content)} parts")

        # Generate content using multimodal content
        response = model.generate_content(multimodal_content)
        response_text = response.text

        # Clean up the response to ensure it's valid JSON
        if '```json' in response_text and '```' in response_text:
            json_content = response_text.split('```json')[1].split('```')[0].strip()
            try:
                analysis_json = json.loads(json_content)
            except json.JSONDecodeError:
                import re
                json_content = re.sub(r',\s*}', '}', json_content)
                json_content = re.sub(r',\s*]', ']', json_content)
                analysis_json = json.loads(json_content)
        else:
            response_text = response_text.strip()
            import re
            json_pattern = r'[\s\S]*?(\{[\s\S]*\})[\s\S]*'
            match = re.match(json_pattern, response_text)
            if match:
                json_text = match.group(1)
                json_text = re.sub(r',\s*}', '}', json_text)
                json_text = re.sub(r',\s*]', ']', json_text)
                analysis_json = json.loads(json_text)
            else:
                analysis_json = json.loads(response_text)

        # Add metadata about the analysis
        analysis_json['analyzed_at'] = datetime.datetime.now().isoformat()
        analysis_json['had_attachments'] = has_attachments

        # Ensure required fields are present with defaults
        if 'category' not in analysis_json:
            analysis_json['category'] = 'other'

        if 'analysis_results' not in analysis_json:
            analysis_json['analysis_results'] = {}
        
        if 'summary' not in analysis_json['analysis_results']:
            analysis_json['analysis_results']['summary'] = "No summary generated"

        if 'sentiment' not in analysis_json['analysis_results']:
            analysis_json['analysis_results']['sentiment'] = 'neutral'

        # Create fingerprint for deduplication
        fingerprint_data = f"{email_data.get('subject', '')}{email_data.get('from', '')}{email_data.get('date', '')}"
        if isinstance(email_data.get('body'), dict):
            body_content = email_data['body'].get('text', email_data['body'].get('html', ''))
            fingerprint_data += body_content[:100]
        elif isinstance(email_data.get('body'), str):
            fingerprint_data += email_data['body'][:100]

        hasher = hashlib.sha256()
        hasher.update(fingerprint_data.encode('utf-8', errors='replace'))
        analysis_json['email_fingerprint'] = hasher.hexdigest()

        return analysis_json
        
    except json.JSONDecodeError as json_err:
        logger.error(f"JSON parsing error: {json_err}")
        logger.error(f"Response text: {response_text[:1000]}...")
        return {
            "error": True,
            "message": f"Error parsing analysis result: {str(json_err)}",
            "raw_response": response_text[:1000]
        }
    except Exception as e:
        logger.error(f"Analysis error: {e}")
        return {
            "error": True,
            "message": f"Error analyzing email: {str(e)}"
        }

async def process_analysis(api_key, analysis_request, user_id, account_id):
    """Process the email analysis in the background with clean JSON structure"""
    try:
        # Get user's Gmail credentials (same as before)
        db = firestore.client()

        account_doc = db.collection('users').document(user_id).collection('email_accounts').document(account_id).get()
        if not account_doc.exists or 'credentials' not in account_doc.to_dict():
            raise ValueError(f"Account {account_id} credentials not found")
        credentials = account_doc.to_dict()['credentials']

        # Get Gmail service
        from routers.gmail_router import get_gmail_service
        service = get_gmail_service(credentials_dict=credentials)

        # Process attachments directly from Gmail (same as before)
        attachment_contents = []
        if analysis_request.email_data.get('attachments') and len(analysis_request.email_data['attachments']) > 0:
            for attachment in analysis_request.email_data['attachments']:
                try:
                    attachment_data = get_attachment_data_from_gmail(
                        service,
                        analysis_request.email_id,
                        attachment
                    )
                    if attachment_data:
                        attachment_contents.append(attachment_data)
                except Exception as e:
                    logger.error(f"Error processing attachment {attachment['filename']}: {str(e)}")

        # Analyze the email with clean structure
        logger.info(f"Starting analysis for email {analysis_request.email_id}")
        analysis_result = analyze_with_gemini_direct(
            api_key,
            analysis_request.email_data,
            analysis_request.attachment_texts,
            attachment_contents
        )

        # Store attachments in Firebase and update URLs in analysis result
        if not analysis_result.get('error', False) and attachment_contents:
            logger.info(f"Analysis successful for email {analysis_request.email_id}")
            from firebase_admin import storage
            bucket = storage.bucket()

            # Update attachments in analysis_result with URLs
            if 'attachments' not in analysis_result:
                analysis_result['attachments'] = []

            for i, attachment in enumerate(attachment_contents):
                try:
                    # Store in Firebase Storage
                    storage_path = f"users/{user_id}/attachments/{analysis_request.email_id}/{attachment['filename']}"
                    blob = bucket.blob(storage_path)

                    with tempfile.NamedTemporaryFile(delete=False) as temp_file:
                        temp_file.write(attachment['data'])
                        temp_file_path = temp_file.name

                    try:
                        blob.upload_from_filename(temp_file_path, content_type=attachment['content_type'])
                        url = f"https://firebasestorage.googleapis.com/v0/b/{bucket.name}/o/{storage_path.replace('/', '%2F')}?alt=media"

                        # Update or add attachment entry in analysis result
                        if i < len(analysis_result['attachments']):
                            analysis_result['attachments'][i]['url'] = url
                            analysis_result['attachments'][i]['size'] = len(attachment['data'])
                        else:
                            # Add new attachment entry
                            attachment_entry = {
                                'filename': attachment['filename'],
                                'content_type': attachment['content_type'],
                                'size': len(attachment['data']),
                                'url': url,
                                'analysis': {
                                    'document_type': 'Document',
                                    'extracted_data_confidence': 0.98,
                                    'content_summary': f"Attachment: {attachment['filename']}"
                                }
                            }
                            analysis_result['attachments'].append(attachment_entry)

                        # Add viewer URL for PDFs and docs
                        content_type = attachment['content_type'].lower()
                        if content_type in ['application/pdf', 'application/msword',
                                          'application/vnd.openxmlformats-officedocument.wordprocessingml.document']:
                            if i < len(analysis_result['attachments']):
                                analysis_result['attachments'][i]['viewer_url'] = f"https://docs.google.com/viewer?url={url}&embedded=true"

                        logger.info(f"Stored attachment in Firebase: {attachment['filename']} with URL: {url}")
                    finally:
                        os.unlink(temp_file_path)
                except Exception as e:
                    logger.error(f"Error storing attachment in Firebase: {str(e)}")

        # Store the analysis result in Firestore with clean structure
        composite_id = f"{account_id}_{analysis_request.email_id}"
        logger.info(f"Storing clean analysis for email {analysis_request.email_id} with composite ID {composite_id}")

        # Prepare clean analysis data for storage
        analysis_data = {
            # Basic metadata
            'created_at': firestore.SERVER_TIMESTAMP,
            'email_id': analysis_request.email_id,
            'composite_id': composite_id,
            'account_id': account_id,
            'user_id': user_id,

            # Email metadata from original email data
            'subject': analysis_request.email_data.get('subject', 'No Subject'),
            'from': analysis_request.email_data.get('from', 'Unknown'),
            'to': analysis_request.email_data.get('to', ''),
            'cc': analysis_request.email_data.get('cc', ''),
            'bcc': analysis_request.email_data.get('bcc', ''),
            'date': analysis_request.email_data.get('date', 'Unknown Date'),
            'has_attachments': bool(analysis_request.email_data.get('attachments')),

            # Store the complete clean analysis result
            'analysis': analysis_result,

            # Extract key fields for easier querying (clean structure)
            'category': analysis_result.get('category', 'other'),
            'summary': analysis_result.get('analysis_results', {}).get('summary', ''),
            'sentiment': analysis_result.get('analysis_results', {}).get('sentiment', 'neutral'),
            'total_amount': analysis_result.get('financial_details', {}).get('total_amount', ''),
            'document_number': analysis_result.get('document_info', {}).get('document_number', ''),
            'po_number': analysis_result.get('document_info', {}).get('po_number', ''),
            'invoice_number': analysis_result.get('document_info', {}).get('invoice_number', ''),
            'vendor_name': analysis_result.get('parties', {}).get('vendor', {}).get('name', ''),
            'customer_name': analysis_result.get('parties', {}).get('customer', {}).get('name', ''),

            # Timestamps
            'analyzed_at': firestore.SERVER_TIMESTAMP,
            'last_updated': firestore.SERVER_TIMESTAMP,

            # Email fingerprint for deduplication
            'email_fingerprint': analysis_result.get('email_fingerprint', ''),

            # Store attachment info
            'attachments': analysis_result.get('attachments', [])
        }

        # Store in Firestore
        try:
            email_analyses_ref = db.collection('users').document(user_id).collection('email_analyses')
            email_analyses_ref.document(composite_id).set(analysis_data)
            logger.info(f"Stored clean analysis in Firestore for {composite_id}")

            # Update the email document to indicate it has been analyzed
            email_ref = db.collection('users').document(user_id).collection('emails').document(composite_id)
            email_ref.update({
                'has_analysis': True,
                'analysis_timestamp': firestore.SERVER_TIMESTAMP
            })

            # Trigger webhook for purchase orders and invoices
            webhook_categories = ['purchase_order', 'invoice', 'order', 'order_confirmation']
            if analysis_result.get('category') in webhook_categories:
                logger.info(f"Triggering webhook for category: {analysis_result.get('category')}")
                
                # Import and call webhook
                from .webhook_sender import send_to_webhook
                
                # Prepare webhook data with email metadata
                webhook_data = {
                    **analysis_data,  # Include all analysis data
                    'attachments': analysis_result.get('attachments', [])  # Ensure attachments with URLs
                }
                
                # Send webhook
                webhook_success = send_to_webhook(webhook_data)
                if webhook_success:
                    logger.info(f"Successfully sent webhook for email {analysis_request.email_id}")
                else:
                    logger.error(f"Failed to send webhook for email {analysis_request.email_id}")

        except Exception as firestore_error:
            logger.error(f"Error storing analysis in Firestore: {str(firestore_error)}")

        logger.info(f"Analysis process completed for email {analysis_request.email_id}")
        
    except Exception as e:
        logger.error(f"Error in background analysis processing: {str(e)}")
        logger.error(f"Traceback: {traceback.format_exc()}")

@router.post("/analyze-email")
async def analyze_email(
    analysis_request: GeminiAnalysisRequest,
    background_tasks: BackgroundTasks,
    user_data: dict = Depends(verify_token)
):
    """Analyze an email and its attachments using Gemini AI with improved handling"""
    try:
        # Apply rate limiting
        if not check_rate_limit(user_data['uid']):
            raise HTTPException(
                status_code=status.HTTP_429_TOO_MANY_REQUESTS,
                detail="Rate limit exceeded. Please try again later."
            )

        # Get Gemini API key from environment variable
        api_key = os.getenv("GEMINI_API_KEY")
        if not api_key:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Gemini API key not found in environment variables"
            )

        user_id = user_data['uid']
        account_id = analysis_request.account_id

        # Check if this email has already been analyzed using the composite ID
        db = firestore.client()
        composite_id = f"{account_id}_{analysis_request.email_id}"
        email_analyses_ref = db.collection('users').document(user_id).collection('email_analyses')
        analysis_doc = email_analyses_ref.document(composite_id).get()
        if analysis_doc.exists:
            logger.info(f"Using existing analysis for email {analysis_request.email_id} from account {account_id}")
            existing_analysis = analysis_doc.to_dict()

            # Check if the email category is 'order', 'purchase order', or 'invoice' and trigger the webhook
            # Only trigger webhook for existing analysis if it hasn't been sent before
            category = existing_analysis.get('category', analysis_request.email_data.get('category', ''))
            if category in ['order', 'purchase order', 'invoice'] and not existing_analysis.get('webhook_sent', False):
                logger.info(f"Triggering webhook for existing analysis: {analysis_request.email_id}")
                send_to_webhook(existing_analysis)
                # Mark webhook as sent
                email_analyses_ref.document(composite_id).update({'webhook_sent': True})

            # Return the existing analysis, prioritizing the nested 'analysis' field if it exists
            if 'analysis' in existing_analysis and isinstance(existing_analysis['analysis'], dict):
                return existing_analysis['analysis']
            return existing_analysis

        # Process in the background for better responsiveness
        background_tasks.add_task(
            process_analysis,
            api_key,
            analysis_request,
            user_id,
            account_id
        )

        # Return a placeholder response while processing continues in the background
        return {
            "message": "Analysis started in the background",
            "email_id": analysis_request.email_id,
            "status": "processing",
            "check_after_seconds": 5  # Suggest client to check back after 5 seconds
        }
    except Exception as e:
        logger.error(f"Error analyzing email: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error analyzing email: {str(e)}"
        )

async def process_analysis(api_key, analysis_request, user_id, account_id):
    """Process the email analysis in the background"""
    try:
        # Get user's Gmail credentials
        db = firestore.client()

        # Get credentials from email_accounts collection
        account_doc = db.collection('users').document(user_id).collection('email_accounts').document(account_id).get()

        if not account_doc.exists or 'credentials' not in account_doc.to_dict():
            raise ValueError(f"Account {account_id} credentials not found")

        credentials = account_doc.to_dict()['credentials']

        # Get Gmail service
        from routers.gmail_router import get_gmail_service
        service = get_gmail_service(credentials_dict=credentials)

        # Process attachments directly from Gmail
        attachment_contents = []
        if analysis_request.email_data.get('attachments') and len(analysis_request.email_data['attachments']) > 0:
            for attachment in analysis_request.email_data['attachments']:
                try:
                    # Get attachment data directly from Gmail
                    attachment_data = get_attachment_data_from_gmail(
                        service,
                        analysis_request.email_id,
                        attachment
                    )
                    attachment_contents.append(attachment_data)
                except Exception as e:
                    logger.error(f"Error processing attachment {attachment['filename']}: {str(e)}")

        # Analyze the email with direct attachment processing
        logger.info(f"Starting analysis for email {analysis_request.email_id}")
        analysis_result = analyze_with_gemini_direct(
            api_key,
            analysis_request.email_data,
            analysis_request.attachment_texts,
            attachment_contents
        )

        # If analysis is successful, store attachments in Firebase
        if not analysis_result.get('error', False):
            logger.info(f"Analysis successful for email {analysis_request.email_id}")
            from firebase_admin import storage
            bucket = storage.bucket()

            # Initialize attachment_analysis array if it doesn't exist
            if 'attachment_analysis' not in analysis_result:
                analysis_result['attachment_analysis'] = []

            # Create a mapping of filenames to attachment analysis entries
            attachment_analysis_map = {}
            for attachment_entry in analysis_result.get('attachment_analysis', []):
                if 'filename' in attachment_entry:
                    attachment_analysis_map[attachment_entry['filename']] = attachment_entry

            # Process each attachment
            for attachment in attachment_contents:
                try:
                    # Store in Firebase Storage
                    storage_path = f"users/{user_id}/attachments/{analysis_request.email_id}/{attachment['filename']}"
                    blob = bucket.blob(storage_path)

                    # Create temporary file for upload
                    with tempfile.NamedTemporaryFile(delete=False) as temp_file:
                        temp_file.write(attachment['data'])
                        temp_file_path = temp_file.name

                    try:
                        # Set content type
                        content_type = attachment['content_type']

                        # Upload with content type
                        blob.upload_from_filename(temp_file_path, content_type=content_type)

                        # Generate URL for the attachment
                        url = f"https://firebasestorage.googleapis.com/v0/b/{bucket.name}/o/{storage_path.replace('/', '%2F')}?alt=media"

                        # Add or update the attachment analysis entry with the URL
                        if attachment['filename'] in attachment_analysis_map:
                            # Update existing entry
                            attachment_analysis_map[attachment['filename']]['url'] = url
                            attachment_analysis_map[attachment['filename']]['content_type'] = content_type
                            attachment_analysis_map[attachment['filename']]['size'] = len(attachment['data'])
                        else:
                            # Create new entry
                            new_entry = {
                                'filename': attachment['filename'],
                                'type': content_type,
                                'content_type': content_type,
                                'size': len(attachment['data']),
                                'url': url,
                                'content_summary': f"Attachment: {attachment['filename']}"
                            }
                            analysis_result['attachment_analysis'].append(new_entry)
                            attachment_analysis_map[attachment['filename']] = new_entry

                        # Ensure the URL is also added to the email_analysis node
                        if 'email_analysis' not in analysis_result:
                            analysis_result['email_analysis'] = {}

                        # Initialize attachment_urls array if it doesn't exist
                        if 'attachment_urls' not in analysis_result['email_analysis']:
                            analysis_result['email_analysis']['attachment_urls'] = []

                        # Add the URL to the email_analysis node
                        attachment_url_entry = {
                            'filename': attachment['filename'],
                            'url': url,
                            'content_type': content_type
                        }
                        analysis_result['email_analysis']['attachment_urls'].append(attachment_url_entry)

                        logger.info(f"Stored attachment in Firebase after analysis: {attachment['filename']} with URL: {url}")
                    finally:
                        # Clean up temp file
                        os.unlink(temp_file_path)
                except Exception as e:
                    logger.error(f"Error storing attachment in Firebase: {str(e)}")

            # Update the analysis result with the modified attachment_analysis
            # This is already done by reference, but we log it for clarity
            logger.info(f"Updated analysis result with {len(analysis_result.get('attachment_analysis', []))} attachment entries including URLs")
        else:
            logger.error(f"Analysis failed for email {analysis_request.email_id}: {analysis_result.get('message', 'Unknown error')}")

        # Store the analysis result in Firestore
        # Use a composite ID that includes the account ID to avoid duplicates across accounts
        composite_id = f"{account_id}_{analysis_request.email_id}"
        logger.info(f"Storing analysis for email {analysis_request.email_id} with composite ID {composite_id}")

        # Create standardized email fingerprint for deduplication
        email_fingerprint = {
            'subject': analysis_request.email_data.get('subject', ''),
            'from': analysis_request.email_data.get('from', ''),
            'to': analysis_request.email_data.get('to', ''),
            'cc': analysis_request.email_data.get('cc', ''),
            'bcc': analysis_request.email_data.get('bcc', ''),
            'date': analysis_request.email_data.get('date', ''),
            'body_hash': hash(str(analysis_request.email_data.get('body', ''))[:1000])  # Use hash of first 1000 chars of body
        }

        # Now use the email_storage module to store the analysis
        try:
            from email_storage import store_email_analysis
            # Prepare the email data in the expected format
            email_data_for_storage = {
                'id': analysis_request.email_id,
                'subject': analysis_request.email_data.get('subject', 'No Subject'),
                'from': analysis_request.email_data.get('from', 'Unknown'),
                'to': analysis_request.email_data.get('to', ''),
                'cc': analysis_request.email_data.get('cc', ''),
                'bcc': analysis_request.email_data.get('bcc', ''),
                'date': analysis_request.email_data.get('date', ''),
                'body': analysis_request.email_data.get('body', ''),
                'account_id': account_id,
                'accountId': account_id
            }

            # Store using the storage module
            storage_result = store_email_analysis(analysis_result, email_data_for_storage, user_id)
            if storage_result:
                logger.info(f"Successfully stored analysis using email_storage module for {composite_id}")
            else:
                logger.error(f"Failed to store analysis using email_storage module for {composite_id}")
        except Exception as storage_error:
            logger.error(f"Error using email_storage module: {str(storage_error)}")

            # Fall back to direct storage
            logger.info(f"Falling back to direct Firestore storage for analysis")

            # Store the analysis result in Firestore with more detailed fields
            analysis_data = {
                # Basic metadata
                'created_at': firestore.SERVER_TIMESTAMP,
                'email_id': analysis_request.email_id,
                'emailId': analysis_request.email_id,
                'composite_id': composite_id,
                'subject': analysis_request.email_data.get('subject', 'No Subject'),
                'from': analysis_request.email_data.get('from', 'Unknown'),
                'to': analysis_request.email_data.get('to', ''),
                'cc': analysis_request.email_data.get('cc', ''),
                'bcc': analysis_request.email_data.get('bcc', ''),
                'date': analysis_request.email_data.get('date', 'Unknown Date'),
                'has_attachments': bool(analysis_request.email_data.get('attachments')),
                'email_fingerprint': email_fingerprint,
                'user_id': user_id,
                'account_id': account_id,
                'accountId': account_id,

                # Store both naming conventions for frontend compatibility
                'is_order': analysis_result.get('is_order', False),
                'isOrder': analysis_result.get('is_order', False),
                'is_invoice': analysis_result.get('is_invoice', False),
                'isInvoice': analysis_result.get('is_invoice', False),
                'category': analysis_result.get('category', 'other'),
                'order_number': analysis_result.get('order_number', ''),
                'orderNumber': analysis_result.get('order_number', ''),
                'vendor': analysis_result.get('vendor', ''),
                'customer': analysis_result.get('customer', ''),
                'total_amount': analysis_result.get('total_amount', ''),
                'totalAmount': analysis_result.get('total_amount', ''),
                'sentiment': analysis_result.get('sentiment', 'neutral'),

                # Key dates and action items
                'key_dates': analysis_result.get('key_dates', []),
                'keyDates': analysis_result.get('key_dates', []),
                'action_items': analysis_result.get('action_items', []),
                'actionItems': analysis_result.get('action_items', []),

                # Email metadata with both naming conventions
                'email_metadata': analysis_result.get('email_metadata', {}),
                'emailMetadata': analysis_result.get('email_metadata', {}),

                # Store attachment analysis separately
                'attachment_analysis': analysis_result.get('attachment_analysis', []),

                # Ensure attachment URLs are also stored in email_analysis node
                'email_analysis': {
                    'summary': analysis_result.get('summary', ''),
                    'is_order': analysis_result.get('is_order', False),
                    'is_invoice': analysis_result.get('is_invoice', False),
                    'category': analysis_result.get('category', ''),
                    'sentiment': analysis_result.get('sentiment', 'neutral'),
                    'key_dates': analysis_result.get('key_dates', []),
                    'action_items': analysis_result.get('action_items', []),
                    'attachment_urls': [
                        {
                            'filename': att.get('filename', ''),
                            'url': att.get('url', ''),
                            'content_type': att.get('content_type', att.get('type', 'application/octet-stream'))
                        }
                        for att in analysis_result.get('attachment_analysis', [])
                        if 'url' in att and 'filename' in att
                    ]
                },

                # Store the full analysis result
                'analysis': analysis_result,

                # Add timestamps for tracking
                'analyzed_at': firestore.SERVER_TIMESTAMP,
                'analyzedAt': firestore.SERVER_TIMESTAMP,
                'last_updated': firestore.SERVER_TIMESTAMP
            }

            # Add order details if available
            if 'order_details' in analysis_result:
                analysis_data['order_details'] = analysis_result['order_details']
                analysis_data['orderDetails'] = analysis_result['order_details']

            # Add items if available
            if 'items' in analysis_result:
                analysis_data['items'] = analysis_result['items']

            # Direct storage in Firestore
            try:
                email_analyses_ref = db.collection('users').document(user_id).collection('email_analyses')
                email_analyses_ref.document(composite_id).set(analysis_data)
                logger.info(f"Directly stored analysis in Firestore for {composite_id}")

                # Update the email document to indicate it has been analyzed
                email_ref = db.collection('users').document(user_id).collection('emails').document(composite_id)
                email_ref.update({
                    'has_analysis': True,
                    'analysis_timestamp': firestore.SERVER_TIMESTAMP
                })
                logger.info(f"Updated email document to mark as analyzed")

                # Verify that the analysis was stored
                verification = email_analyses_ref.document(composite_id).get()
                if verification.exists:
                    logger.info(f"Verification successful - analysis document exists in Firestore")
                else:
                    logger.error(f"Verification failed - analysis document does not exist in Firestore after save attempt")
            except Exception as firestore_error:
                logger.error(f"Error storing analysis directly in Firestore: {str(firestore_error)}")

        logger.info(f"Analysis process completed for email {analysis_request.email_id}")
    except Exception as e:
        logger.error(f"Error in background analysis processing: {str(e)}")

@router.post("/extract-pdf")
async def extract_pdf_data(
    extraction_request: PdfExtractionRequest,
    user_data: dict = Depends(verify_token)
):
    """Extract data from a PDF attachment using Gemini AI"""
    try:
        # Apply rate limiting
        if not check_rate_limit(user_data['uid']):
            raise HTTPException(
                status_code=status.HTTP_429_TOO_MANY_REQUESTS,
                detail="Rate limit exceeded. Please try again later."
            )

        # Get Gemini API key from environment variable
        api_key = os.getenv("GEMINI_API_KEY")
        if not api_key:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Gemini API key not found in environment variables"
            )

        # Download the PDF from Firebase Storage
        bucket = storage.bucket()
        blob = bucket.blob(f"users/{user_data['uid']}/attachments/{extraction_request.email_id}/{extraction_request.filename}")

        # Create a temporary file
        with tempfile.NamedTemporaryFile(delete=False, suffix='.pdf') as temp_file:
            blob.download_to_filename(temp_file.name)
            temp_file_path = temp_file.name

        try:
            # Extract data from the PDF
            extraction_result = send_pdf_to_gemini(api_key, temp_file_path)

            # Store the extraction result in Firestore
            db = firestore.client()
            composite_id = f"{extraction_request.account_id}_{extraction_request.email_id}"

            db.collection('users').document(user_data['uid']).collection('pdf_extractions').document(composite_id).set({
                'extraction': extraction_result,
                'filename': extraction_request.filename,
                'email_id': extraction_request.email_id,
                'account_id': extraction_request.account_id,
                'composite_id': composite_id,
                'created_at': firestore.SERVER_TIMESTAMP
            })

            return extraction_result
        finally:
            # Delete the temporary file
            os.unlink(temp_file_path)
    except Exception as e:
        logger.error(f"Error extracting PDF data: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error extracting PDF data: {str(e)}"
        )

@router.post("/upload-and-extract")
async def upload_and_extract_pdf(
    file: UploadFile = File(...),
    user_data: dict = Depends(verify_token)
):
    """Upload a PDF file and extract data using Gemini AI"""
    try:
        # Apply rate limiting
        if not check_rate_limit(user_data['uid']):
            raise HTTPException(
                status_code=status.HTTP_429_TOO_MANY_REQUESTS,
                detail="Rate limit exceeded. Please try again later."
            )

        # Check if file is a PDF
        if not file.content_type == "application/pdf":
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Only PDF files are supported"
            )

        # Get Gemini API key from environment variable
        api_key = os.getenv("GEMINI_API_KEY")
        if not api_key:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Gemini API key not found in environment variables"
            )

        # Create a temporary file
        with tempfile.NamedTemporaryFile(delete=False, suffix='.pdf') as temp_file:
            # Write the uploaded file content to the temporary file
            content = await file.read()
            temp_file.write(content)
            temp_file_path = temp_file.name

        try:
            # Extract data from the PDF
            extraction_result = send_pdf_to_gemini(api_key, temp_file_path)

            # Upload the file to Firebase Storage
            bucket = storage.bucket()
            safe_filename = ''.join(c for c in file.filename if c.isalnum() or c in '._- ')
            storage_path = f"uploads/{user_data['uid']}/{safe_filename}"
            blob = bucket.blob(storage_path)
            blob.upload_from_filename(temp_file_path, content_type="application/pdf")

            # Make the blob publicly accessible
            blob.make_public()
            file_url = blob.public_url

            # Store the extraction result in Firestore
            db = firestore.client()
            doc_ref = db.collection('users').document(user_data['uid']).collection('pdf_extractions').add({
                'extraction': extraction_result,
                'filename': file.filename,
                'url': file_url,
                'created_at': firestore.SERVER_TIMESTAMP
            })

            return {
                "extraction": extraction_result,
                "file_url": file_url,
                "document_id": doc_ref[1].id
            }
        finally:
            # Delete the temporary file
            os.unlink(temp_file_path)
    except Exception as e:
        logger.error(f"Error uploading and extracting PDF: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error uploading and extracting PDF: {str(e)}"
        )

@router.post("/batch-analyze")
async def batch_analyze_emails(
    request: BatchAnalyzeRequest,
    background_tasks: BackgroundTasks,
    user_data: dict = Depends(verify_token)
):
    """Batch analyze multiple emails with improved handling"""
    try:
        # Apply rate limiting
        if not check_rate_limit(user_data['uid']):
            raise HTTPException(
                status_code=status.HTTP_429_TOO_MANY_REQUESTS,
                detail="Rate limit exceeded. Please try again later."
            )

        # Get Gemini API key from environment variable
        api_key = os.getenv("GEMINI_API_KEY")
        if not api_key:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Gemini API key not found in environment variables"
            )

        # Start the batch analysis in the background
        background_tasks.add_task(
            process_batch_analysis,
            request.query,
            request.max_results,
            api_key,
            user_data['uid'],
            request.account_id
        )

        # Return a placeholder response
        return {
            "status": "processing",
            "message": "Batch analysis started in the background",
            "query": request.query,
            "max_results": request.max_results,
            "account_id": request.account_id
        }
    except Exception as e:
        logger.error(f"Error starting batch analysis: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error starting batch analysis: {str(e)}"
        )

async def process_batch_analysis(query: str, max_results: int, api_key: str, user_id: str, account_id: str):
    """Process the batch analysis in the background"""
    try:
        # Get credentials
        db = firestore.client()

        # Get credentials based on account ID
        account_doc = db.collection('users').document(user_id).collection('email_accounts').document(account_id).get()
        
        if not account_doc.exists:
            logger.error(f"Account {account_id} not found for user {user_id}")
            return
        
        account_data = account_doc.to_dict()
        
        if 'credentials' not in account_data:
            logger.error(f"No credentials found for account {account_id} for user {user_id}")
            return
            
        credentials = account_data['credentials']

        # Get Gmail service
        from routers.gmail_router import get_gmail_service, search_messages, get_message_details
        service = get_gmail_service(credentials_dict=credentials)

        # Search for emails
        logger.info(f"Searching for emails with query: {query}")
        message_ids = search_messages(service, search_string=query, max_results=max_results)

        if not message_ids:
            logger.info("No emails found for the query")
            # Update batch task status
            db.collection('users').document(user_id).collection('batch_tasks').add({
                'type': 'batch_analysis',
                'query': query,
                'account_id': account_id,
                'status': 'completed',
                'result': 'No emails found',
                'created_at': firestore.SERVER_TIMESTAMP,
                'completed_at': firestore.SERVER_TIMESTAMP
            })
            return

        logger.info(f"Found {len(message_ids)} emails matching the query")

        # Create batch task record
        batch_task_ref = db.collection('users').document(user_id).collection('batch_tasks').add({
            'type': 'batch_analysis',
            'query': query,
            'account_id': account_id,
            'status': 'processing',
            'total_emails': len(message_ids),
            'processed_emails': 0,
            'created_at': firestore.SERVER_TIMESTAMP
        })

        batch_task_id = batch_task_ref[1].id

        # Check which emails have already been analyzed
        email_analyses_ref = db.collection('users').document(user_id).collection('email_analyses')
        # We can't easily query for multiple composite IDs, so we'll check individually

        processed_count = 0
        # Process emails in smaller batches to avoid rate limits
        for i in range(0, len(message_ids), 5):  # Process in batches of 5
            batch_ids = message_ids[i:i+5]

            for msg_id in batch_ids:
                try:
                    # Check if this email has already been analyzed
                    composite_id = f"{account_id}_{msg_id}"
                    analysis_doc = email_analyses_ref.document(composite_id).get()

                    if analysis_doc.exists:
                        logger.info(f"Email {msg_id} already analyzed, skipping")
                        processed_count += 1

                        # Update batch task status
                        db.collection('users').document(user_id).collection('batch_tasks').document(batch_task_id).update({
                            'processed_emails': processed_count,
                            'updated_at': firestore.SERVER_TIMESTAMP
                        })

                        continue

                    # Get email details
                    email_details = get_message_details(service, msg_id=msg_id)

                    # Add account ID to email data
                    email_details['accountId'] = account_id

                    # Store the raw email
                    from email_storage import store_raw_email
                    store_raw_email(email_details, user_id, account_id)

                    # Process attachments
                    attachment_contents = []
                    if email_details.get('attachments') and len(email_details['attachments']) > 0:
                        for attachment in email_details['attachments']:
                            try:
                                # Get attachment data directly from Gmail
                                attachment_data = get_attachment_data_from_gmail(
                                    service,
                                    msg_id,
                                    attachment
                                )
                                attachment_contents.append(attachment_data)
                            except Exception as e:
                                logger.error(f"Error processing attachment {attachment['filename']}: {str(e)}")

                    # Analyze the email
                    analysis_result = analyze_with_gemini_direct(
                        api_key,
                        email_details,
                        None,  # No attachment texts
                        attachment_contents
                    )

                    # Store the analysis result
                    if not analysis_result.get('error', False):
                        # Store attachments if needed
                        bucket = storage.bucket()
                        for attachment in attachment_contents:
                            try:
                                # Store in Firebase Storage
                                storage_path = f"users/{user_id}/attachments/{msg_id}/{attachment['filename']}"
                                blob = bucket.blob(storage_path)

                                # Create temporary file for upload
                                with tempfile.NamedTemporaryFile(delete=False) as temp_file:
                                    temp_file.write(attachment['data'])
                                    temp_file_path = temp_file.name

                                try:
                                    # Set content type
                                    content_type = attachment['content_type']

                                    # Upload with content type
                                    blob.upload_from_filename(temp_file_path, content_type=content_type)
                                finally:
                                    # Clean up temp file
                                    os.unlink(temp_file_path)
                            except Exception as e:
                                logger.error(f"Error storing attachment in Firebase: {str(e)}")

                        # Create email fingerprint for deduplication
                        email_fingerprint = {
                            'subject': email_details.get('subject', ''),
                            'from': email_details.get('from', ''),
                            'to': email_details.get('to', ''),
                            'cc': email_details.get('cc', ''),
                            'bcc': email_details.get('bcc', ''),
                            'date': email_details.get('date', ''),
                            'body_hash': hash(str(email_details.get('body', ''))[:1000])  # Use hash of first 1000 chars of body
                        }

                        # Store the analysis result in Firestore
                        analysis_data = {
                            # Basic metadata
                            'created_at': firestore.SERVER_TIMESTAMP,
                            'email_id': msg_id,
                            'emailId': msg_id,
                            'composite_id': composite_id,
                            'subject': email_details.get('subject', 'No Subject'),
                            'from': email_details.get('from', 'Unknown'),
                            'to': email_details.get('to', ''),
                            'cc': email_details.get('cc', ''),
                            'bcc': email_details.get('bcc', ''),
                            'date': email_details.get('date', 'Unknown Date'),
                            'has_attachments': bool(email_details.get('attachments')),
                            'email_fingerprint': email_fingerprint,
                            'user_id': user_id,
                            'account_id': account_id,
                            'accountId': account_id,

                            # Store both naming conventions for frontend compatibility
                            'is_order': analysis_result.get('is_order', False),
                            'isOrder': analysis_result.get('is_order', False),
                            'is_invoice': analysis_result.get('is_invoice', False),
                            'isInvoice': analysis_result.get('is_invoice', False),
                            'category': analysis_result.get('category', 'other'),
                            'order_number': analysis_result.get('order_number', ''),
                            'orderNumber': analysis_result.get('order_number', ''),
                            'vendor': analysis_result.get('vendor', ''),
                            'customer': analysis_result.get('customer', ''),
                            'total_amount': analysis_result.get('total_amount', ''),
                            'totalAmount': analysis_result.get('total_amount', ''),
                            'sentiment': analysis_result.get('sentiment', 'neutral'),

                            # Key dates and action items
                            'key_dates': analysis_result.get('key_dates', []),
                            'keyDates': analysis_result.get('key_dates', []),
                            'action_items': analysis_result.get('action_items', []),
                            'actionItems': analysis_result.get('action_items', []),

                            # Email metadata with both naming conventions
                            'email_metadata': analysis_result.get('email_metadata', {}),
                            'emailMetadata': analysis_result.get('email_metadata', {}),

                            # Store attachment analysis separately
                            'attachment_analysis': analysis_result.get('attachment_analysis', []),

                            # Ensure attachment URLs are also stored in email_analysis node
                            'email_analysis': {
                                'summary': analysis_result.get('summary', ''),
                                'is_order': analysis_result.get('is_order', False),
                                'is_invoice': analysis_result.get('is_invoice', False),
                                'category': analysis_result.get('category', ''),
                                'sentiment': analysis_result.get('sentiment', 'neutral'),
                                'key_dates': analysis_result.get('key_dates', []),
                                'action_items': analysis_result.get('action_items', []),
                                'attachment_urls': [
                                    {
                                        'filename': att.get('filename', ''),
                                        'url': att.get('url', ''),
                                        'content_type': att.get('content_type', att.get('type', 'application/octet-stream'))
                                    }
                                    for att in analysis_result.get('attachment_analysis', [])
                                    if 'url' in att and 'filename' in att
                                ]
                            },

                            # Store the full analysis result
                            'analysis': analysis_result,

                            # Add timestamps for tracking
                            'analyzed_at': firestore.SERVER_TIMESTAMP,
                            'analyzedAt': firestore.SERVER_TIMESTAMP,
                            'last_updated': firestore.SERVER_TIMESTAMP
                        }

                        # Add order details if available
                        if 'order_details' in analysis_result:
                            analysis_data['order_details'] = analysis_result['order_details']
                            analysis_data['orderDetails'] = analysis_result['order_details']

                        # Add items if available
                        if 'items' in analysis_result:
                            analysis_data['items'] = analysis_result['items']

                        # Store in Firestore
                        email_analyses_ref.document(composite_id).set(analysis_data)

                        # Update the email document to indicate it has been analyzed
                        email_ref = db.collection('users').document(user_id).collection('emails').document(composite_id)
                        email_ref.update({
                            'has_analysis': True,
                            'analysis_timestamp': firestore.SERVER_TIMESTAMP
                        })

                    processed_count += 1

                    # Update batch task status
                    db.collection('users').document(user_id).collection('batch_tasks').document(batch_task_id).update({
                        'processed_emails': processed_count,
                        'updated_at': firestore.SERVER_TIMESTAMP
                    })

                    # Add delay to avoid rate limits
                    import asyncio
                    await asyncio.sleep(1)

                except Exception as e:
                    logger.error(f"Error processing email {msg_id}: {str(e)}")
                    # Continue with next email

            # Add delay between batches to avoid rate limits
            import asyncio
            await asyncio.sleep(3)

        # Update batch task status to completed
        db.collection('users').document(user_id).collection('batch_tasks').document(batch_task_id).update({
            'status': 'completed',
            'processed_emails': processed_count,
            'completed_at': firestore.SERVER_TIMESTAMP
        })

        logger.info(f"Batch analysis completed: {processed_count}/{len(message_ids)} emails processed")
    except Exception as e:
        logger.error(f"Error in background batch analysis: {str(e)}")

        # Update batch task status to failed
        if 'batch_task_id' in locals() and 'db' in locals():
            try:
                db.collection('users').document(user_id).collection('batch_tasks').document(batch_task_id).update({
                    'status': 'failed',
                    'error': str(e),
                    'completed_at': firestore.SERVER_TIMESTAMP
                })
                logger.info(f"Updated batch task {batch_task_id} status to failed")
            except Exception as update_error:
                logger.error(f"Failed to update batch task status: {str(update_error)}")

@router.get("/batch-status/{batch_task_id}")
async def get_batch_status(
    batch_task_id: str,
    user_data: dict = Depends(verify_token)
):
    """Get the status of a batch analysis task"""
    try:
        # Apply rate limiting
        if not check_rate_limit(user_data['uid']):
            raise HTTPException(
                status_code=status.HTTP_429_TOO_MANY_REQUESTS,
                detail="Rate limit exceeded. Please try again later."
            )

        db = firestore.client()
        task_doc = db.collection('users').document(user_data['uid']).collection('batch_tasks').document(batch_task_id).get()

        if not task_doc.exists:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Batch task not found"
            )

        task_data = task_doc.to_dict()

        return {
            "task_id": batch_task_id,
            "status": task_data.get('status'),
            "query": task_data.get('query'),
            "account_id": task_data.get('account_id'),
            "total_emails": task_data.get('total_emails'),
            "processed_emails": task_data.get('processed_emails'),
            "created_at": task_data.get('created_at'),
            "updated_at": task_data.get('updated_at'),
            "completed_at": task_data.get('completed_at'),
            "error": task_data.get('error')
        }
    except HTTPException as e:
        raise e
    except Exception as e:
        logger.error(f"Error getting batch status: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error getting batch status: {str(e)}"
        )

@router.post("/analyze-document")
async def analyze_generic_document(
    file: UploadFile = File(...),
    user_data: dict = Depends(verify_token)
):
    """Analyze a generic document using Gemini AI"""
    try:
        # Apply rate limiting
        if not check_rate_limit(user_data['uid']):
            raise HTTPException(
                status_code=status.HTTP_429_TOO_MANY_REQUESTS,
                detail="Rate limit exceeded. Please try again later."
            )

        # Get Gemini API key from environment variable
        api_key = os.getenv("GEMINI_API_KEY")
        if not api_key:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Gemini API key not found in environment variables"
            )

        # Process different file types accordingly
        file_ext = os.path.splitext(file.filename)[1].lower()
        content = await file.read()

        # Create a temporary file
        with tempfile.NamedTemporaryFile(delete=False, suffix=file_ext) as temp_file:
            temp_file.write(content)
            temp_file_path = temp_file.name

        try:
            # Configure the Gemini API
            genai.configure(api_key=api_key)
            model = genai.GenerativeModel('gemini-2.5-flash')

            # Extract text based on file type
            extracted_text = extract_text_from_attachment(temp_file_path)

            # Create prompt based on file type
            if file_ext in ['.pdf', '.docx', '.doc']:
                prompt = """
                Analyze this document and extract key information including:
                1. Document type (e.g., invoice, order, contract, report)
                2. Key dates, deadlines, or timeframes
                3. Financial information (amounts, prices, totals)
                4. People, companies, or organizations mentioned
                5. Main topics or subject matter
                6. Action items or next steps

                Format your response as a JSON object with clear sections for each type of information.
                """
            elif file_ext in ['.xlsx', '.xls', '.csv']:
                prompt = """
                Analyze this spreadsheet data and provide:
                1. Summary of what the data represents
                2. Key metrics or statistics (totals, averages, etc.)
                3. Any patterns or trends you observe
                4. Main categories or groupings of data
                5. Potential insights or conclusions

                Format your response as a JSON object with clear sections for each type of information.
                """
            else:
                prompt = """
                Analyze this document content and extract key information including:
                1. Document type or purpose
                2. Main topics or subject matter
                3. Key entities mentioned (people, places, organizations)
                4. Any dates, numbers, or metrics
                5. Action items or important points

                Format your response as a JSON object with clear sections for each type of information.
                """

            # Generate content using the extracted text and prompt
            response = model.generate_content([prompt, extracted_text])

            # Get the response text
            response_text = response.text

            # Try to parse as JSON
            try:
                if '```json' in response_text and '```' in response_text:
                    # Extract JSON from code block
                    json_content = response_text.split('```json')[1].split('```')[0].strip()
                    analysis_json = json.loads(json_content)
                else:
                    # Try to parse the entire text as JSON
                    analysis_json = json.loads(response_text.strip())
            except json.JSONDecodeError:
                # Fall back to text response if JSON parsing fails
                analysis_json = {
                    "document_analysis": response_text,
                    "format": "text"
                }

            # Add metadata
            analysis_json['filename'] = file.filename
            analysis_json['file_type'] = file.content_type
            analysis_json['file_size'] = len(content)
            analysis_json['analyzed_at'] = datetime.datetime.now().isoformat()

            # Store the file in Firebase Storage
            bucket = storage.bucket()
            safe_filename = ''.join(c for c in file.filename if c.isalnum() or c in '._- ')
            storage_path = f"uploads/{user_data['uid']}/documents/{safe_filename}"
            blob = bucket.blob(storage_path)
            blob.upload_from_filename(temp_file_path, content_type=file.content_type)

            # Get a download URL
            blob.make_public()
            file_url = blob.public_url

            # Store the analysis in Firestore
            db = firestore.client()
            doc_ref = db.collection('users').document(user_data['uid']).collection('document_analyses').add({
                'filename': file.filename,
                'file_type': file.content_type,
                'file_size': len(content),
                'url': file_url,
                'analysis': analysis_json,
                'created_at': firestore.SERVER_TIMESTAMP
            })

            return {
                "analysis": analysis_json,
                "file_url": file_url,
                "document_id": doc_ref[1].id
            }
        finally:
            # Delete the temporary file
            os.unlink(temp_file_path)
    except Exception as e:
        logger.error(f"Error analyzing document: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error analyzing document: {str(e)}"
        )