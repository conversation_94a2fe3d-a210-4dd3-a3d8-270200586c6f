#!/usr/bin/env python3
"""
Test script to verify the integrated main fixes
"""

import os
import sys
import logging
import json
import base64

# Add backend directory to Python path
sys.path.append(os.path.join(os.path.dirname(__file__), 'backend'))

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger("test_integrated_fix")

def create_test_pubsub_message(email_address: str, history_id: str):
    """Create a test Pub/Sub message"""
    notification_data = {
        "emailAddress": email_address,
        "historyId": history_id
    }
    
    # Encode the notification data
    encoded_data = base64.b64encode(json.dumps(notification_data).encode('utf-8')).decode('utf-8')
    
    return {
        "message": {
            "data": encoded_data,
            "messageId": "test-message-id",
            "publishTime": "2023-01-01T00:00:00.000Z"
        }
    }

def test_pubsub_parsing():
    """Test the Pub/Sub message parsing"""
    try:
        # Import the integrated processor
        from integrated_main import IntegratedGmailProcessor
        
        processor = IntegratedGmailProcessor()
        
        # Create test message
        test_message = create_test_pubsub_message("<EMAIL>", "12345")
        
        # Parse the message
        notification = processor.parse_pubsub_message(test_message)
        
        if notification:
            logger.info(f"✅ Successfully parsed notification: {notification.emailAddress}, historyId: {notification.historyId}")
            return True
        else:
            logger.error("❌ Failed to parse notification")
            return False
            
    except Exception as e:
        logger.error(f"❌ Test failed: {str(e)}")
        return False

if __name__ == "__main__":
    logger.info("🧪 Testing integrated main fixes...")
    
    # Test Pub/Sub parsing
    if test_pubsub_parsing():
        logger.info("✅ All tests passed!")
    else:
        logger.error("❌ Tests failed!")
        sys.exit(1)
